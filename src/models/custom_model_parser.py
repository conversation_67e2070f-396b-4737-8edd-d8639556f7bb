import os
import re
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum

# 移除智能推荐功能相关的导入

logger = logging.getLogger(__name__)


class ModelType(Enum):
    """支持的模型类型"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    AZURE = "azure"
    HUGGINGFACE = "huggingface"
    LOCAL = "local"


@dataclass
class CustomModelConfig:
    """自定义模型配置"""
    name: str
    base_url: str
    api_key: str
    model_id: str
    model_type: ModelType
    additional_params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.additional_params is None:
            self.additional_params = {}


class CustomModelParser:
    """自定义模型环境变量解析器"""
    
    # 环境变量前缀
    CUSTOM_MODEL_PREFIX = "CUSTOM_MODEL_"
    AGENT_MODEL_PREFIX = "AGENT_"
    
    # 支持的Agent角色
    SUPPORTED_AGENTS = {
        "PLANNING_AGENT",
        "DEEP_RESEARCHER_AGENT", 
        "DEEP_ANALYZER_AGENT",
        "BROWSER_USE_AGENT",
        # 保持向后兼容的小写形式
        "planning_agent",
        "deep_researcher_agent",
        "deep_analyzer_agent", 
        "browser_use_agent"
    }
    
    # 支持的参数
    SUPPORTED_PARAMS = {
        "BASE_URL",
        "API_KEY", 
        "MODEL_ID",
        "MODEL_TYPE",
        "MAX_TOKENS",
        "TEMPERATURE",
        "TIMEOUT",
        "PROXY"
    }
    
    # 必需的参数 (用于Agent直接配置)
    AGENT_REQUIRED_PARAMS = {
        "BASE_URL",
        "API_KEY",
        "MODEL_ID",
        "MODEL_TYPE"
    }
    
    # 必需的参数 (用于自定义模型配置)
    REQUIRED_PARAMS = {
        "BASE_URL",
        "API_KEY",
        "MODEL_ID",
        "MODEL_TYPE"
    }
    
    def __init__(self):
        self.custom_models: Dict[str, CustomModelConfig] = {}
        self.agent_model_assignments: Dict[str, str] = {}
        # 新增：Agent直接配置存储
        self.agent_direct_configs: Dict[str, CustomModelConfig] = {}
        
    def parse_environment_variables(self) -> None:
        """解析环境变量中的自定义模型配置"""
        logger.info("开始解析自定义模型环境变量...")
        
        # 解析自定义模型配置
        self._parse_custom_models()
        
        # 解析智能体模型分配 (旧方式，保持兼容性)
        self._parse_agent_assignments()
        
        # 解析Agent直接配置 (新方式)
        self._parse_agent_direct_configs()
        
        logger.info(f"成功解析 {len(self.custom_models)} 个自定义模型配置")
        logger.info(f"成功解析 {len(self.agent_model_assignments)} 个智能体模型分配")
        logger.info(f"成功解析 {len(self.agent_direct_configs)} 个智能体直接配置")
        
    def _parse_custom_models(self) -> None:
        """解析自定义模型配置"""
        custom_model_vars = {}
        
        # 收集所有自定义模型相关的环境变量
        for key, value in os.environ.items():
            if key.startswith(self.CUSTOM_MODEL_PREFIX):
                # 提取模型名称和参数名
                # 格式: CUSTOM_MODEL_{MODEL_NAME}_{PARAM}
                parts = key[len(self.CUSTOM_MODEL_PREFIX):].split('_')
                if len(parts) >= 2:
                    model_name = '_'.join(parts[:-1])
                    param_name = parts[-1]
                    
                    if param_name in self.SUPPORTED_PARAMS:
                        if model_name not in custom_model_vars:
                            custom_model_vars[model_name] = {}
                        custom_model_vars[model_name][param_name] = value
        
        # 解析每个模型的配置
        for model_name, params in custom_model_vars.items():
            try:
                config = self._create_model_config(model_name, params)
                self.custom_models[model_name] = config
                logger.info(f"成功解析自定义模型: {model_name}")
            except ValueError as e:
                logger.error(f"解析自定义模型 {model_name} 失败: {e}")
                
    def _create_model_config(self, model_name: str, params: Dict[str, str]) -> CustomModelConfig:
        """创建模型配置对象"""
        # 检查必需参数
        missing_params = self.REQUIRED_PARAMS - set(params.keys())
        if missing_params:
            raise ValueError(f"缺少必需参数: {missing_params}")
        
        # 验证模型类型
        try:
            model_type = ModelType(params["MODEL_TYPE"].lower())
        except ValueError:
            raise ValueError(f"不支持的模型类型: {params['MODEL_TYPE']}")
        
        # 提取额外参数
        additional_params = {}
        for key, value in params.items():
            if key not in self.REQUIRED_PARAMS:
                additional_params[key.lower()] = value
        
        return CustomModelConfig(
            name=model_name,
            base_url=params["BASE_URL"],
            api_key=params["API_KEY"],
            model_id=params["MODEL_ID"],
            model_type=model_type,
            additional_params=additional_params
        )
        
    def _parse_agent_assignments(self) -> None:
        """解析智能体模型分配"""
        for key, value in os.environ.items():
            if key.startswith(self.AGENT_MODEL_PREFIX):
                # 提取智能体名称
                # 格式: AGENT_{AGENT_NAME}_MODEL
                agent_name = key[len(self.AGENT_MODEL_PREFIX):-6]  # 去掉 "_MODEL"
                if agent_name:
                    self.agent_model_assignments[agent_name] = value
                    logger.info(f"智能体 {agent_name} 分配模型: {value}")
    
    def _parse_agent_direct_configs(self) -> None:
        """解析Agent直接配置"""
        agent_config_vars = {}
        
        # 收集所有Agent直接配置的环境变量
        for key, value in os.environ.items():
            # 检查是否为支持的Agent配置
            for agent_name in self.SUPPORTED_AGENTS:
                if key.startswith(f"{agent_name}_"):
                    param_name = key[len(agent_name) + 1:]  # +1 for the underscore
                    
                    if param_name in self.SUPPORTED_PARAMS:
                        if agent_name not in agent_config_vars:
                            agent_config_vars[agent_name] = {}
                        agent_config_vars[agent_name][param_name] = value
                        break
        
        # 为每个Agent创建配置
        for agent_name, params in agent_config_vars.items():
            try:
                # 检查是否有足够的参数来创建完整配置
                if all(param in params for param in self.AGENT_REQUIRED_PARAMS):
                    config = self._create_agent_model_config(agent_name, params)
                    # 标准化Agent名称 (转换为小写形式用于内部使用)
                    normalized_name = agent_name.lower()
                    self.agent_direct_configs[normalized_name] = config
                    logger.info(f"成功解析Agent直接配置: {normalized_name}")
                else:
                    missing_params = self.AGENT_REQUIRED_PARAMS - set(params.keys())
                    logger.warning(f"Agent {agent_name} 配置不完整，缺少参数: {missing_params}")
            except ValueError as e:
                logger.error(f"解析Agent配置 {agent_name} 失败: {e}")
                
    def _create_agent_model_config(self, agent_name: str, params: Dict[str, str]) -> CustomModelConfig:
        """为Agent创建模型配置对象"""
        # 验证模型类型
        try:
            model_type = ModelType(params["MODEL_TYPE"].lower())
        except ValueError:
            raise ValueError(f"不支持的模型类型: {params['MODEL_TYPE']}")
        
        # 提取额外参数
        additional_params = {}
        for key, value in params.items():
            if key not in self.AGENT_REQUIRED_PARAMS:
                # 转换参数名为小写
                additional_params[key.lower()] = value
        
        return CustomModelConfig(
            name=f"{agent_name.lower()}_direct_config",
            base_url=params["BASE_URL"],
            api_key=params["API_KEY"],
            model_id=params["MODEL_ID"],
            model_type=model_type,
            additional_params=additional_params
        )
                    
    def get_custom_model(self, model_name: str) -> Optional[CustomModelConfig]:
        """获取自定义模型配置"""
        return self.custom_models.get(model_name)
        
    def get_agent_model(self, agent_name: str) -> Optional[str]:
        """获取智能体分配的模型名称"""
        return self.agent_model_assignments.get(agent_name)
    
    def get_agent_direct_config(self, agent_name: str) -> Optional[CustomModelConfig]:
        """获取Agent直接配置"""
        # 标准化Agent名称
        normalized_name = agent_name.lower()
        return self.agent_direct_configs.get(normalized_name)
    
    def get_agent_model_config(self, agent_name: str) -> Optional[CustomModelConfig]:
        """获取Agent的模型配置 (优先使用直接配置，然后是分配配置)"""
        # 首先尝试获取直接配置
        direct_config = self.get_agent_direct_config(agent_name)
        if direct_config:
            return direct_config
        
        # 如果没有直接配置，尝试获取分配的自定义模型
        assigned_model = self.get_agent_model(agent_name)
        if assigned_model:
            return self.get_custom_model(assigned_model)
        
        return None
        
    def get_all_custom_models(self) -> Dict[str, CustomModelConfig]:
        """获取所有自定义模型配置"""
        return self.custom_models.copy()
        
    def get_all_agent_assignments(self) -> Dict[str, str]:
        """获取所有智能体模型分配"""
        return self.agent_model_assignments.copy()
        
    def validate_model_config(self, config: CustomModelConfig) -> bool:
        """验证模型配置"""
        try:
            # 验证必需字段
            if not config.name or not config.base_url or not config.api_key or not config.model_id:
                return False
                
            # 验证 URL 格式
            if not self._is_valid_url(config.base_url):
                return False
                
            # 验证模型类型
            if not isinstance(config.model_type, ModelType):
                return False
                
            return True
        except Exception:
            return False
            
    def _is_valid_url(self, url: str) -> bool:
        """验证 URL 格式"""
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        return url_pattern.match(url) is not None
        
    def get_model_config_for_agent(self, agent_name: str) -> Optional[CustomModelConfig]:
        """获取指定智能体的模型配置"""
        model_name = self.get_agent_model(agent_name)
        if model_name:
            return self.get_custom_model(model_name)
        return None
        
    def list_custom_models(self) -> List[str]:
        """列出所有自定义模型名称"""
        return list(self.custom_models.keys())
        
    def list_agent_assignments(self) -> List[str]:
        """列出所有智能体分配"""
        return list(self.agent_model_assignments.keys())
        
    def remove_custom_model(self, model_name: str) -> bool:
        """移除自定义模型"""
        if model_name in self.custom_models:
            del self.custom_models[model_name]
            logger.info(f"已移除自定义模型: {model_name}")
            return True
        return False
        
    def update_custom_model(self, model_name: str, config: CustomModelConfig) -> bool:
        """更新自定义模型配置"""
        if self.validate_model_config(config):
            self.custom_models[model_name] = config
            logger.info(f"已更新自定义模型: {model_name}")
            return True
        return False
        
    def reload_config(self) -> None:
        """重新加载配置"""
        logger.info("重新加载自定义模型配置...")
        self.custom_models.clear()
        self.agent_model_assignments.clear()
        self.agent_direct_configs.clear()
        self.parse_environment_variables()
        
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        summary = {
            "custom_models_count": len(self.custom_models),
            "agent_assignments_count": len(self.agent_model_assignments),
            "agent_direct_configs_count": len(self.agent_direct_configs),
            "custom_models": list(self.custom_models.keys()),
            "agent_assignments": list(self.agent_model_assignments.keys()),
            "agent_direct_configs": list(self.agent_direct_configs.keys())
        }
        
        return summary
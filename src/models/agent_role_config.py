"""
智能体角色配置系统
实现基于角色特征的智能模型推荐和配置管理
"""

import os
import json
import logging
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path

logger = logging.getLogger(__name__)


class TaskComplexity(Enum):
    """任务复杂度等级"""
    SIMPLE = "simple"          # 简单任务，基础对话
    MODERATE = "moderate"      # 中等任务，需要推理
    COMPLEX = "complex"        # 复杂任务，需要深度分析
    EXPERT = "expert"          # 专家级任务，需要专业知识


class ResponseStyle(Enum):
    """响应风格"""
    CONCISE = "concise"        # 简洁直接
    DETAILED = "detailed"      # 详细解释
    ANALYTICAL = "analytical"  # 分析性的
    CREATIVE = "creative"      # 创造性的


class ModelCapability(Enum):
    """模型能力类型"""
    REASONING = "reasoning"         # 推理能力
    ANALYSIS = "analysis"          # 分析能力
    CREATIVITY = "creativity"      # 创造力
    CODING = "coding"              # 编程能力
    RESEARCH = "research"          # 研究能力
    PLANNING = "planning"          # 规划能力
    INTERACTION = "interaction"    # 交互能力
    MULTIMODAL = "multimodal"     # 多模态能力


@dataclass
class AgentRoleProfile:
    """智能体角色配置文件"""
    role_name: str                                    # 角色名称
    description: str                                  # 角色描述
    primary_capabilities: List[ModelCapability]       # 主要能力需求
    task_complexity: TaskComplexity                   # 任务复杂度
    response_style: ResponseStyle                     # 响应风格
    context_length_requirement: int = 8000            # 上下文长度需求
    reasoning_depth: str = "moderate"                 # 推理深度：shallow, moderate, deep
    domain_expertise: List[str] = None                # 领域专业知识
    performance_priority: str = "balanced"            # 性能优先级：speed, balanced, quality
    token_efficiency: str = "balanced"                # token效率：efficient, balanced, verbose
    
    def __post_init__(self):
        if self.domain_expertise is None:
            self.domain_expertise = []


@dataclass 
class ModelRecommendation:
    """模型推荐结果"""
    model_name: str                    # 推荐的模型名称
    confidence_score: float            # 推荐置信度 (0-1)
    reasoning: str                     # 推荐理由
    capability_match: Dict[str, float] # 能力匹配度
    alternatives: List[str] = None     # 备选模型
    
    def __post_init__(self):
        if self.alternatives is None:
            self.alternatives = []


@dataclass
class ModelProfile:
    """模型配置文件"""
    model_name: str                                   # 模型名称
    model_type: str                                   # 模型类型 (openai, anthropic, etc.)
    capabilities: Dict[ModelCapability, float]        # 能力评分 (0-10)
    max_context_length: int                          # 最大上下文长度
    reasoning_strength: float                        # 推理能力强度 (0-10)
    speed_rating: float                              # 速度评分 (0-10)
    cost_rating: float                               # 成本评分 (0-10, 10最便宜)
    specialties: List[str] = None                    # 专业领域
    limitations: List[str] = None                    # 已知限制
    
    def __post_init__(self):
        if self.specialties is None:
            self.specialties = []
        if self.limitations is None:
            self.limitations = []


class AgentRoleConfigManager:
    """智能体角色配置管理器"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录，默认为 src/config/agent_roles/
        """
        if config_dir is None:
            config_dir = os.path.join(os.path.dirname(__file__), "../config/agent_roles")
        
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        # 角色配置文件
        self.role_profiles: Dict[str, AgentRoleProfile] = {}
        # 模型配置文件
        self.model_profiles: Dict[str, ModelProfile] = {}
        # 用户自定义配置
        self.user_overrides: Dict[str, str] = {}
        # 动态配置缓存
        self.runtime_cache: Dict[str, Dict[str, Any]] = {}
        
        # 加载配置
        self._load_configurations()
    
    def _load_configurations(self) -> None:
        """加载所有配置文件"""
        try:
            # 加载默认角色配置
            self._load_default_role_profiles()
            # 加载默认模型配置  
            self._load_default_model_profiles()
            # 加载用户自定义配置
            self._load_user_configurations()
            # 从环境变量加载配置
            self._load_from_environment()
            
            logger.info(f"成功加载 {len(self.role_profiles)} 个角色配置")
            logger.info(f"成功加载 {len(self.model_profiles)} 个模型配置")
            
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            raise
    
    def _load_default_role_profiles(self) -> None:
        """加载默认角色配置"""
        default_roles = {
            "planning_agent": AgentRoleProfile(
                role_name="planning_agent",
                description="规划协调角色，负责任务分解和协调其他智能体",
                primary_capabilities=[
                    ModelCapability.PLANNING,
                    ModelCapability.REASONING,
                    ModelCapability.ANALYSIS
                ],
                task_complexity=TaskComplexity.COMPLEX,
                response_style=ResponseStyle.ANALYTICAL,
                context_length_requirement=16000,
                reasoning_depth="deep",
                domain_expertise=["project_management", "task_decomposition", "coordination"],
                performance_priority="quality",
                token_efficiency="balanced"
            ),
            
            "deep_researcher_agent": AgentRoleProfile(
                role_name="deep_researcher_agent", 
                description="深度研究角色，负责信息收集和知识获取",
                primary_capabilities=[
                    ModelCapability.RESEARCH,
                    ModelCapability.ANALYSIS,
                    ModelCapability.REASONING
                ],
                task_complexity=TaskComplexity.EXPERT,
                response_style=ResponseStyle.DETAILED,
                context_length_requirement=32000,
                reasoning_depth="deep",
                domain_expertise=["information_retrieval", "data_analysis", "research_methodology"],
                performance_priority="quality", 
                token_efficiency="verbose"
            ),
            
            "deep_analyzer_agent": AgentRoleProfile(
                role_name="deep_analyzer_agent",
                description="深度分析角色，负责数据分析和洞察提取",
                primary_capabilities=[
                    ModelCapability.ANALYSIS,
                    ModelCapability.REASONING,
                    ModelCapability.CODING
                ],
                task_complexity=TaskComplexity.EXPERT,
                response_style=ResponseStyle.ANALYTICAL,
                context_length_requirement=24000,
                reasoning_depth="deep",
                domain_expertise=["data_analysis", "statistical_analysis", "pattern_recognition"],
                performance_priority="quality",
                token_efficiency="balanced"
            ),
            
            "browser_use_agent": AgentRoleProfile(
                role_name="browser_use_agent",
                description="浏览器操作角色，负责网页交互和信息抓取",
                primary_capabilities=[
                    ModelCapability.INTERACTION,
                    ModelCapability.MULTIMODAL,
                    ModelCapability.REASONING
                ],
                task_complexity=TaskComplexity.MODERATE,
                response_style=ResponseStyle.CONCISE,
                context_length_requirement=12000,
                reasoning_depth="moderate",
                domain_expertise=["web_automation", "ui_interaction", "data_extraction"],
                performance_priority="speed",
                token_efficiency="efficient"
            )
        }
        
        self.role_profiles.update(default_roles)
    
    def _load_default_model_profiles(self) -> None:
        """加载默认模型配置"""
        default_models = {
            "claude-3.7-sonnet-thinking": ModelProfile(
                model_name="claude-3.7-sonnet-thinking",
                model_type="anthropic",
                capabilities={
                    ModelCapability.REASONING: 9.5,
                    ModelCapability.ANALYSIS: 9.0,
                    ModelCapability.PLANNING: 8.5,
                    ModelCapability.RESEARCH: 8.0,
                    ModelCapability.CODING: 8.5,
                    ModelCapability.CREATIVITY: 8.0,
                    ModelCapability.INTERACTION: 7.5,
                    ModelCapability.MULTIMODAL: 7.0
                },
                max_context_length=200000,
                reasoning_strength=9.5,
                speed_rating=7.0,
                cost_rating=6.0,
                specialties=["complex_reasoning", "analysis", "planning"],
                limitations=["speed_sensitive_tasks"]
            ),
            
            "gpt-4.1": ModelProfile(
                model_name="gpt-4.1", 
                model_type="openai",
                capabilities={
                    ModelCapability.REASONING: 9.0,
                    ModelCapability.ANALYSIS: 8.5,
                    ModelCapability.PLANNING: 8.0,
                    ModelCapability.RESEARCH: 7.5,
                    ModelCapability.CODING: 9.0,
                    ModelCapability.CREATIVITY: 8.5,
                    ModelCapability.INTERACTION: 8.5,
                    ModelCapability.MULTIMODAL: 9.0
                },
                max_context_length=128000,
                reasoning_strength=9.0,
                speed_rating=8.0,
                cost_rating=5.0,
                specialties=["coding", "multimodal", "general_purpose"],
                limitations=["very_long_context"]
            ),
            
            "claude-3-haiku": ModelProfile(
                model_name="claude-3-haiku",
                model_type="anthropic", 
                capabilities={
                    ModelCapability.REASONING: 7.5,
                    ModelCapability.ANALYSIS: 7.0,
                    ModelCapability.PLANNING: 6.5,
                    ModelCapability.RESEARCH: 7.0,
                    ModelCapability.CODING: 7.5,
                    ModelCapability.CREATIVITY: 7.0,
                    ModelCapability.INTERACTION: 8.0,
                    ModelCapability.MULTIMODAL: 6.5
                },
                max_context_length=200000,
                reasoning_strength=7.5,
                speed_rating=9.5,
                cost_rating=9.0,
                specialties=["speed", "efficiency", "simple_tasks"],
                limitations=["complex_reasoning"]
            ),
            
            "gpt-3.5-turbo": ModelProfile(
                model_name="gpt-3.5-turbo",
                model_type="openai",
                capabilities={
                    ModelCapability.REASONING: 7.0,
                    ModelCapability.ANALYSIS: 6.5,
                    ModelCapability.PLANNING: 6.0,
                    ModelCapability.RESEARCH: 6.5,
                    ModelCapability.CODING: 7.0,
                    ModelCapability.CREATIVITY: 7.5,
                    ModelCapability.INTERACTION: 8.0,
                    ModelCapability.MULTIMODAL: 5.0
                },
                max_context_length=16000,
                reasoning_strength=7.0,
                speed_rating=9.0,
                cost_rating=9.5,
                specialties=["speed", "cost_efficiency", "general_purpose"],
                limitations=["complex_analysis", "long_context"]
            )
        }
        
        self.model_profiles.update(default_models)
    
    def _load_user_configurations(self) -> None:
        """加载用户自定义配置"""
        user_config_file = self.config_dir / "user_config.json"
        if user_config_file.exists():
            try:
                with open(user_config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    self.user_overrides = user_config.get("agent_model_overrides", {})
                    logger.info(f"加载用户配置: {len(self.user_overrides)} 个覆盖配置")
            except Exception as e:
                logger.warning(f"加载用户配置失败: {e}")
    
    def _load_from_environment(self) -> None:
        """从环境变量加载配置"""
        # 加载用户覆盖配置
        for key, value in os.environ.items():
            if key.startswith("AGENT_") and key.endswith("_MODEL_OVERRIDE"):
                agent_name = key[6:-15].lower()  # 移除前缀和后缀
                self.user_overrides[agent_name] = value
                logger.info(f"从环境变量加载模型覆盖: {agent_name} -> {value}")
    
    def recommend_model_for_role(self, role_name: str) -> ModelRecommendation:
        """
        为指定角色推荐最适合的模型
        
        Args:
            role_name: 角色名称
            
        Returns:
            ModelRecommendation: 推荐结果
        """
        if role_name not in self.role_profiles:
            raise ValueError(f"未知角色: {role_name}")
        
        role_profile = self.role_profiles[role_name]
        
        # 检查用户是否有覆盖配置
        if role_name in self.user_overrides:
            override_model = self.user_overrides[role_name]
            return ModelRecommendation(
                model_name=override_model,
                confidence_score=1.0,
                reasoning=f"用户指定模型覆盖: {override_model}",
                capability_match={}
            )
        
        # 计算每个模型的匹配分数
        model_scores = {}
        for model_name, model_profile in self.model_profiles.items():
            score = self._calculate_model_score(role_profile, model_profile)
            model_scores[model_name] = score
        
        # 排序并选择最佳模型
        sorted_models = sorted(model_scores.items(), key=lambda x: x[1]['total_score'], reverse=True)
        
        if not sorted_models:
            raise ValueError("没有可用的模型配置")
        
        best_model, best_score = sorted_models[0]
        alternatives = [model for model, _ in sorted_models[1:4]]  # 前3个备选
        
        return ModelRecommendation(
            model_name=best_model,
            confidence_score=best_score['total_score'] / 10.0,  # 标准化到0-1
            reasoning=self._generate_recommendation_reasoning(role_profile, self.model_profiles[best_model], best_score),
            capability_match=best_score['capability_scores'],
            alternatives=alternatives
        )
    
    def _calculate_model_score(self, role_profile: AgentRoleProfile, model_profile: ModelProfile) -> Dict[str, Any]:
        """计算模型与角色的匹配分数"""
        capability_scores = {}
        total_score = 0.0
        
        # 1. 主要能力匹配 (权重40%)
        capability_weight = 0.4
        capability_score = 0.0
        for capability in role_profile.primary_capabilities:
            if capability in model_profile.capabilities:
                score = model_profile.capabilities[capability]
                capability_scores[capability.value] = score
                capability_score += score
        
        if role_profile.primary_capabilities:
            capability_score /= len(role_profile.primary_capabilities)
        
        total_score += capability_score * capability_weight
        
        # 2. 任务复杂度匹配 (权重25%)
        complexity_weight = 0.25
        complexity_score = self._match_complexity(role_profile.task_complexity, model_profile.reasoning_strength)
        total_score += complexity_score * complexity_weight
        
        # 3. 上下文长度需求 (权重15%)
        context_weight = 0.15
        context_score = 10.0 if model_profile.max_context_length >= role_profile.context_length_requirement else 3.0
        total_score += context_score * context_weight
        
        # 4. 性能优先级匹配 (权重10%)
        performance_weight = 0.10
        performance_score = self._match_performance_priority(role_profile.performance_priority, model_profile)
        total_score += performance_score * performance_weight
        
        # 5. Token效率匹配 (权重10%)
        efficiency_weight = 0.10
        efficiency_score = self._match_token_efficiency(role_profile.token_efficiency, model_profile.cost_rating)
        total_score += efficiency_score * efficiency_weight
        
        return {
            'total_score': total_score,
            'capability_scores': capability_scores,
            'complexity_score': complexity_score,
            'context_score': context_score,
            'performance_score': performance_score,
            'efficiency_score': efficiency_score
        }
    
    def _match_complexity(self, required_complexity: TaskComplexity, model_reasoning: float) -> float:
        """匹配任务复杂度和模型推理能力"""
        complexity_map = {
            TaskComplexity.SIMPLE: (5.0, 7.0),    # 简单任务需要中等推理能力
            TaskComplexity.MODERATE: (6.0, 8.5),   # 中等任务需要较好推理能力  
            TaskComplexity.COMPLEX: (7.5, 10.0),   # 复杂任务需要强推理能力
            TaskComplexity.EXPERT: (8.5, 10.0)     # 专家任务需要顶级推理能力
        }
        
        min_required, max_useful = complexity_map[required_complexity]
        
        if model_reasoning < min_required:
            return 3.0  # 能力不足
        elif model_reasoning > max_useful:
            return 8.0  # 过度配置，但仍然可用
        else:
            return 10.0  # 完美匹配
    
    def _match_performance_priority(self, priority: str, model_profile: ModelProfile) -> float:
        """匹配性能优先级"""
        if priority == "speed":
            return model_profile.speed_rating
        elif priority == "quality":
            return model_profile.reasoning_strength
        else:  # balanced
            return (model_profile.speed_rating + model_profile.reasoning_strength) / 2.0
    
    def _match_token_efficiency(self, efficiency: str, cost_rating: float) -> float:
        """匹配Token效率需求"""
        if efficiency == "efficient":
            return cost_rating  # 成本评分越高越好
        elif efficiency == "verbose":
            return 10.0 - cost_rating  # 不太关心成本
        else:  # balanced
            return 7.0  # 中性分数
    
    def _generate_recommendation_reasoning(self, role_profile: AgentRoleProfile, 
                                         model_profile: ModelProfile, 
                                         scores: Dict[str, Any]) -> str:
        """生成推荐理由"""
        reasons = []
        
        # 主要能力匹配
        if scores['capability_scores']:
            avg_capability = sum(scores['capability_scores'].values()) / len(scores['capability_scores'])
            if avg_capability >= 8.0:
                reasons.append(f"优秀的能力匹配 (平均分: {avg_capability:.1f}/10)")
            elif avg_capability >= 6.0:
                reasons.append(f"良好的能力匹配 (平均分: {avg_capability:.1f}/10)")
        
        # 复杂度匹配
        if scores['complexity_score'] >= 8.0:
            reasons.append(f"推理能力与任务复杂度匹配良好")
        
        # 上下文长度
        if scores['context_score'] >= 8.0:
            reasons.append(f"支持所需的上下文长度 ({role_profile.context_length_requirement})")
        
        # 性能优先级
        if role_profile.performance_priority == "speed" and model_profile.speed_rating >= 8.0:
            reasons.append("高速度评分满足速度优先需求")
        elif role_profile.performance_priority == "quality" and model_profile.reasoning_strength >= 8.0:
            reasons.append("高推理能力满足质量优先需求")
        
        if not reasons:
            reasons.append(f"综合评分最高的模型 (总分: {scores['total_score']:.1f}/10)")
        
        return "; ".join(reasons)
    
    def get_model_for_agent(self, agent_name: str) -> str:
        """
        获取指定智能体应该使用的模型
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            str: 模型名称
        """
        recommendation = self.recommend_model_for_role(agent_name)
        return recommendation.model_name
    
    def set_user_override(self, agent_name: str, model_name: str, save_to_file: bool = True) -> None:
        """
        设置用户模型覆盖
        
        Args:
            agent_name: 智能体名称
            model_name: 指定的模型名称
            save_to_file: 是否保存到配置文件
        """
        self.user_overrides[agent_name] = model_name
        
        if save_to_file:
            self._save_user_config()
        
        logger.info(f"设置用户覆盖: {agent_name} -> {model_name}")
    
    def remove_user_override(self, agent_name: str, save_to_file: bool = True) -> None:
        """
        移除用户模型覆盖
        
        Args:
            agent_name: 智能体名称  
            save_to_file: 是否保存到配置文件
        """
        if agent_name in self.user_overrides:
            del self.user_overrides[agent_name]
            
            if save_to_file:
                self._save_user_config()
            
            logger.info(f"移除用户覆盖: {agent_name}")
    
    def _save_user_config(self) -> None:
        """保存用户配置到文件"""
        user_config_file = self.config_dir / "user_config.json"
        try:
            config_data = {
                "agent_model_overrides": self.user_overrides,
                "last_updated": str(Path(__file__).stat().st_mtime)
            }
            
            with open(user_config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存用户配置失败: {e}")
    
    def add_custom_role(self, role_profile: AgentRoleProfile) -> None:
        """添加自定义角色配置"""
        self.role_profiles[role_profile.role_name] = role_profile
        logger.info(f"添加自定义角色: {role_profile.role_name}")
    
    def add_custom_model(self, model_profile: ModelProfile) -> None:
        """添加自定义模型配置"""
        self.model_profiles[model_profile.model_name] = model_profile
        logger.info(f"添加自定义模型: {model_profile.model_name}")
    
    def get_configuration_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        summary = {}
        
        # 角色配置摘要
        for role_name, role_profile in self.role_profiles.items():
            recommendation = self.recommend_model_for_role(role_name)
            summary[role_name] = {
                "description": role_profile.description,
                "task_complexity": role_profile.task_complexity.value,
                "recommended_model": recommendation.model_name,
                "confidence": f"{recommendation.confidence_score:.2f}",
                "user_override": self.user_overrides.get(role_name, None)
            }
        
        return summary
    
    def reload_configurations(self) -> None:
        """重新加载所有配置"""
        logger.info("重新加载角色配置...")
        self.role_profiles.clear()
        self.model_profiles.clear() 
        self.user_overrides.clear()
        self.runtime_cache.clear()
        self._load_configurations()


# 全局配置管理器实例
_role_config_manager = None

def get_role_config_manager() -> AgentRoleConfigManager:
    """获取全局角色配置管理器实例"""
    global _role_config_manager
    if _role_config_manager is None:
        _role_config_manager = AgentRoleConfigManager()
    return _role_config_manager
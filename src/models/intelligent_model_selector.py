"""
智能模型选择器
整合角色配置和自定义模型解析，提供统一的模型选择接口
"""

import os
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass

from .custom_model_parser import CustomModelParser, CustomModelConfig
from .agent_role_config import get_role_config_manager, AgentRoleConfigManager, ModelRecommendation

logger = logging.getLogger(__name__)


@dataclass
class ModelSelectionResult:
    """模型选择结果"""
    agent_name: str                    # 智能体名称
    selected_model: str               # 选中的模型名称
    selection_method: str             # 选择方法：direct, role_based, default
    custom_config: Optional[CustomModelConfig] = None  # 自定义模型配置
    recommendation: Optional[ModelRecommendation] = None  # 推荐详情
    fallback_used: bool = False       # 是否使用了fallback


class IntelligentModelSelector:
    """智能模型选择器 - 统一的模型选择接口"""
    
    def __init__(self, enable_role_based: bool = True):
        """
        初始化模型选择器
        
        Args:
            enable_role_based: 是否启用基于角色的推荐
        """
        self.custom_parser = CustomModelParser()
        self.role_config_manager: Optional[AgentRoleConfigManager] = None
        self.enable_role_based = enable_role_based
        
        # 解析环境变量配置
        self.custom_parser.parse_environment_variables()
        
        # 根据环境变量决定是否启用角色配置
        enable_env = os.getenv("ENABLE_ROLE_BASED_MODEL_RECOMMENDATION", "true").lower()
        if enable_env in ("true", "1", "yes") and enable_role_based:
            self._enable_role_based_selection()
    
    def _enable_role_based_selection(self) -> None:
        """启用基于角色的模型选择"""
        try:
            self.custom_parser.enable_role_based_config()
            self.role_config_manager = self.custom_parser.role_config_manager
            if self.role_config_manager:
                logger.info("成功启用基于角色的智能模型选择")
            else:
                logger.warning("角色配置管理器初始化失败")
        except Exception as e:
            logger.error(f"启用角色配置失败: {e}")
            self.role_config_manager = None
    
    def select_model_for_agent(self, agent_name: str, 
                             fallback_model: Optional[str] = None) -> ModelSelectionResult:
        """
        为指定智能体选择最适合的模型
        
        Args:
            agent_name: 智能体名称
            fallback_model: 备用模型名称
            
        Returns:
            ModelSelectionResult: 选择结果
        """
        result = ModelSelectionResult(
            agent_name=agent_name,
            selected_model="",
            selection_method="unknown"
        )
        
        try:
            # 1. 尝试直接分配（最高优先级）
            direct_model = self.custom_parser.get_agent_model(agent_name)
            if direct_model:
                result.selected_model = direct_model
                result.selection_method = "direct"
                result.custom_config = self.custom_parser.get_custom_model(direct_model)
                logger.info(f"使用直接分配模型: {agent_name} -> {direct_model}")
                return result
            
            # 2. 尝试基于角色的智能推荐
            if self.role_config_manager:
                try:
                    recommendation = self.role_config_manager.recommend_model_for_role(agent_name)
                    result.selected_model = recommendation.model_name
                    result.selection_method = "role_based"
                    result.recommendation = recommendation
                    result.custom_config = self.custom_parser.get_custom_model(recommendation.model_name)
                    
                    logger.info(f"使用角色推荐模型: {agent_name} -> {recommendation.model_name} "
                              f"(置信度: {recommendation.confidence_score:.2f})")
                    return result
                    
                except Exception as e:
                    logger.warning(f"角色推荐失败，尝试fallback: {e}")
            
            # 3. 使用fallback模型
            if fallback_model:
                result.selected_model = fallback_model
                result.selection_method = "fallback"
                result.fallback_used = True
                result.custom_config = self.custom_parser.get_custom_model(fallback_model)
                logger.info(f"使用fallback模型: {agent_name} -> {fallback_model}")
                return result
            
            # 4. 最后尝试从配置中获取默认模型
            result.selected_model = self._get_default_model_for_agent(agent_name)
            result.selection_method = "default"
            logger.info(f"使用默认模型: {agent_name} -> {result.selected_model}")
            
        except Exception as e:
            logger.error(f"模型选择过程出错: {e}")
            # 最后的保险措施
            result.selected_model = self._get_emergency_fallback_model()
            result.selection_method = "emergency"
            result.fallback_used = True
        
        return result
    
    def _get_default_model_for_agent(self, agent_name: str) -> str:
        """获取智能体的默认模型"""
        # 基于智能体类型的默认模型映射
        default_models = {
            "planning_agent": "claude-3.7-sonnet-thinking",
            "deep_researcher_agent": "claude-3.7-sonnet-thinking", 
            "deep_analyzer_agent": "claude-3.7-sonnet-thinking",
            "browser_use_agent": "gpt-4.1"
        }
        
        return default_models.get(agent_name, "claude-3.7-sonnet-thinking")
    
    def _get_emergency_fallback_model(self) -> str:
        """获取紧急备用模型"""
        # 从环境变量或配置中获取紧急备用模型
        emergency_model = os.getenv("EMERGENCY_FALLBACK_MODEL", "claude-3.7-sonnet-thinking")
        logger.warning(f"使用紧急备用模型: {emergency_model}")
        return emergency_model
    
    def get_model_details(self, model_name: str) -> Optional[Dict[str, Any]]:
        """
        获取模型的详细信息
        
        Args:
            model_name: 模型名称
            
        Returns:
            Dict: 模型详细信息
        """
        details = {}
        
        # 获取自定义模型配置
        custom_config = self.custom_parser.get_custom_model(model_name)
        if custom_config:
            details["custom_config"] = {
                "name": custom_config.name,
                "base_url": custom_config.base_url,
                "model_id": custom_config.model_id,
                "model_type": custom_config.model_type.value,
                "additional_params": custom_config.additional_params
            }
        
        # 获取角色配置中的模型信息
        if self.role_config_manager and model_name in self.role_config_manager.model_profiles:
            model_profile = self.role_config_manager.model_profiles[model_name]
            details["role_profile"] = {
                "capabilities": {cap.value: score for cap, score in model_profile.capabilities.items()},
                "max_context_length": model_profile.max_context_length,
                "reasoning_strength": model_profile.reasoning_strength,
                "speed_rating": model_profile.speed_rating,
                "cost_rating": model_profile.cost_rating,
                "specialties": model_profile.specialties,
                "limitations": model_profile.limitations
            }
        
        return details if details else None
    
    def get_all_available_models(self) -> Dict[str, Dict[str, Any]]:
        """获取所有可用模型的信息"""
        all_models = {}
        
        # 添加自定义模型
        for model_name in self.custom_parser.list_custom_models():
            all_models[model_name] = {
                "source": "custom",
                "details": self.get_model_details(model_name)
            }
        
        # 添加角色配置中的模型
        if self.role_config_manager:
            for model_name in self.role_config_manager.model_profiles.keys():
                if model_name not in all_models:
                    all_models[model_name] = {
                        "source": "role_profile",
                        "details": self.get_model_details(model_name)
                    }
                else:
                    # 合并信息
                    all_models[model_name]["source"] = "both"
        
        return all_models
    
    def get_agent_selection_summary(self) -> Dict[str, Dict[str, Any]]:
        """获取所有智能体的模型选择摘要"""
        summary = {}
        
        # 获取所有已知的智能体
        all_agents = set(self.custom_parser.list_agent_assignments())
        if self.role_config_manager:
            all_agents.update(self.role_config_manager.role_profiles.keys())
        
        # 如果没有配置任何智能体，使用默认列表
        if not all_agents:
            all_agents = {"planning_agent", "deep_researcher_agent", "deep_analyzer_agent", "browser_use_agent"}
        
        for agent_name in all_agents:
            selection_result = self.select_model_for_agent(agent_name)
            
            summary[agent_name] = {
                "selected_model": selection_result.selected_model,
                "selection_method": selection_result.selection_method,
                "has_custom_config": selection_result.custom_config is not None,
                "recommendation_confidence": (
                    selection_result.recommendation.confidence_score 
                    if selection_result.recommendation else None
                ),
                "fallback_used": selection_result.fallback_used
            }
        
        return summary
    
    def set_agent_model(self, agent_name: str, model_name: str, 
                       persistent: bool = True) -> None:
        """
        设置智能体使用的模型
        
        Args:
            agent_name: 智能体名称
            model_name: 模型名称
            persistent: 是否持久化保存
        """
        self.custom_parser.set_agent_model_override(agent_name, model_name, persistent)
        logger.info(f"设置智能体模型: {agent_name} -> {model_name}")
    
    def remove_agent_model(self, agent_name: str, persistent: bool = True) -> None:
        """
        移除智能体的模型设置，恢复到推荐模型
        
        Args:
            agent_name: 智能体名称
            persistent: 是否从持久化配置中移除
        """
        self.custom_parser.remove_agent_model_override(agent_name, persistent)
        logger.info(f"移除智能体模型设置: {agent_name}")
    
    def add_custom_model(self, model_config: CustomModelConfig) -> None:
        """
        添加自定义模型配置
        
        Args:
            model_config: 自定义模型配置
        """
        if self.custom_parser.validate_model_config(model_config):
            self.custom_parser.update_custom_model(model_config.name, model_config)
            logger.info(f"添加自定义模型: {model_config.name}")
        else:
            raise ValueError(f"无效的模型配置: {model_config.name}")
    
    def get_configuration_report(self) -> Dict[str, Any]:
        """获取完整的配置报告"""
        report = {
            "selector_status": {
                "role_based_enabled": self.role_config_manager is not None,
                "custom_models_count": len(self.custom_parser.custom_models),
                "agent_assignments_count": len(self.custom_parser.agent_model_assignments)
            },
            "agent_selections": self.get_agent_selection_summary(),
            "available_models": list(self.get_all_available_models().keys()),
            "custom_parser_summary": self.custom_parser.get_config_summary()
        }
        
        if self.role_config_manager:
            report["role_based_summary"] = self.role_config_manager.get_configuration_summary()
        
        return report
    
    def reload_configurations(self) -> None:
        """重新加载所有配置"""
        logger.info("重新加载模型选择器配置...")
        
        # 重新加载自定义模型配置
        self.custom_parser.reload_config()
        
        # 重新加载角色配置
        if self.role_config_manager:
            self.role_config_manager.reload_configurations()
        
        logger.info("配置重新加载完成")


# 全局模型选择器实例
_model_selector = None

def get_model_selector() -> IntelligentModelSelector:
    """获取全局模型选择器实例"""
    global _model_selector
    if _model_selector is None:
        _model_selector = IntelligentModelSelector()
    return _model_selector
[{"name": "extract_colored_numbers_from_image", "description": "Identifies and extracts numbers from an image, categorizing them by color (e.g., red, green).", "function": null, "metadata": {"name": "extract_colored_numbers_from_image", "description": "Identifies and extracts numbers from an image, categorizing them by color (e.g., red, green).", "requires": "cv2, pytesseract, numpy, re", "args": ["image_path (str): Path to the input image file.", "color_definitions (dict): A dictionary defining the colors to extract. Keys are color names (str), values are dictionaries with 'lower' and 'upper' HSV boundary lists.", "tesseract_config (str): Configuration string for Tesseract OCR.", "min_contour_area (int): The minimum area for a contour to be considered a number."], "returns": ["result (dict): A dictionary where keys are color names and values are lists of extracted integers."]}, "script_content": "```python\n# MCP Name: extract_colored_numbers_from_image\n# Description: Identifies and extracts numbers from an image, categorizing them by color (e.g., red, green).\n# Arguments:\n#   image_path (str): Path to the input image file.\n#   color_definitions (dict): A dictionary defining the colors to extract. Keys are color names (str), values are dictionaries with 'lower' and 'upper' HSV boundary lists.\n#   tesseract_config (str): Configuration string for Tesseract OCR.\n#   min_contour_area (int): The minimum area for a contour to be considered a number.\n# Returns:\n#   result (dict): A dictionary where keys are color names and values are lists of extracted integers.\n# Requires: cv2, pytesseract, numpy, re\n\nimport cv2\nimport pytesseract\nimport numpy as np\nimport re\nfrom collections import defaultdict\n\ndef extract_colored_numbers_from_image(image_path: str, color_definitions: dict, tesseract_config: str, min_contour_area: int):\n    \"\"\"\n    Identifies and extracts numbers from an image, categorizing them by color.\n\n    Args:\n        image_path (str): Path to the input image file.\n        color_definitions (dict): A dictionary defining the colors to extract.\n                                  Example: {'red': {'lower': [170, 100, 100], 'upper': [10, 255, 255]}}\n                                  Note: For red, a lower hue > upper hue signifies a wrap-around the 180-degree mark in HSV.\n        tesseract_config (str): Configuration string for Tesseract OCR (e.g., '--psm 10 -c tessedit_char_whitelist=0123456789').\n        min_contour_area (int): The minimum pixel area for a contour to be processed as a potential number, used to filter out noise.\n\n    Returns:\n        dict: A dictionary where keys are the color names from color_definitions and\n              values are lists of integers extracted for that color.\n    \"\"\"\n    try:\n        # Load the image from the specified path\n        image = cv2.imread(image_path)\n        if image is None:\n            raise FileNotFoundError(f\"Image not found at path: {image_path}\")\n\n        # Convert the image from BGR to HSV color space for better color segmentation\n        hsv_image = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)\n\n        extracted_numbers = defaultdict(list)\n\n        for color_name, bounds in color_definitions.items():\n            lower_bound = np.array(bounds['lower'])\n            upper_bound = np.array(bounds['upper'])\n\n            # Create a mask for the current color.\n            # This handles the case for colors like red, which wrap around the 0/180 hue value in HSV.\n            if lower_bound[0] > upper_bound[0]:\n                # Lower range (e.g., 170-180)\n                mask1 = cv2.inRange(hsv_image, np.array([lower_bound[0], lower_bound[1], lower_bound[2]]), np.array([180, upper_bound[1], upper_bound[2]]))\n                # Upper range (e.g., 0-10)\n                mask2 = cv2.inRange(hsv_image, np.array([0, lower_bound[1], lower_bound[2]]), np.array([upper_bound[0], upper_bound[1], upper_bound[2]]))\n                # Combine the two masks\n                mask = cv2.add(mask1, mask2)\n            else:\n                # For non-wrapping colors like green\n                mask = cv2.inRange(hsv_image, lower_bound, upper_bound)\n\n            # Find contours of the colored areas in the mask\n            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)\n\n            for contour in contours:\n                # Filter out contours that are too small to be numbers, based on the parameter\n                if cv2.contourArea(contour) < min_contour_area:\n                    continue\n\n                # Get the bounding box for the contour\n                x, y, w, h = cv2.boundingRect(contour)\n                \n                # Isolate the region of interest (the number) from the mask\n                # Using the mask provides a clean, high-contrast image for OCR\n                roi = mask[y:y+h, x:x+w]\n\n                # Use Tesseract to perform OCR on the isolated region\n                text = pytesseract.image_to_string(roi, config=tesseract_config)\n\n                # Clean the OCR output to ensure only digits are processed\n                digits = re.sub(r'\\D', '', text)\n\n                if digits:\n                    extracted_numbers[color_name].append(int(digits))\n\n        return dict(extracted_numbers)\n\n    except Exception as e:\n        # Return a descriptive error message if anything goes wrong\n        return f\"Error: {str(e)}\"\n```", "created_at": "2025-07-25T20:42:40.108529", "usage_count": 8, "last_used": "2025-07-28T21:42:06.753126"}, {"name": "calculate_average_of_deviations", "description": "Takes two lists of numbers. It calculates the population standard deviation (pstdev) of the first list and the sample standard deviation (stdev) of the second list using <PERSON>'s statistics module. It then returns the average of these two results, rounded to three decimal points.", "function": null, "metadata": {"name": "calculate_average_of_deviations", "description": "Takes two lists of numbers. It calculates the population standard deviation (pstdev) of the first list and the sample standard deviation (stdev) of the second list using <PERSON>'s statistics module. It then returns the average of these two results, rounded to three decimal points.", "requires": "statistics, typing", "args": ["population_data (list): A list of numbers for which to calculate the population standard deviation.", "sample_data (list): A list of numbers for which to calculate the sample standard deviation.", "rounding_precision (int): The number of decimal places to round the final result to."], "returns": ["result (float): The rounded average of the two standard deviation calculations."]}, "script_content": "```python\nimport statistics\nfrom typing import List, Union\n\n# MCP Name: calculate_average_of_deviations\n# Description: Takes two lists of numbers. It calculates the population standard deviation (pstdev) of the first list and the sample standard deviation (stdev) of the second list using <PERSON>'s statistics module. It then returns the average of these two results, rounded to three decimal points.\n# Arguments:\n#   population_data (list): A list of numbers for which to calculate the population standard deviation.\n#   sample_data (list): A list of numbers for which to calculate the sample standard deviation.\n#   rounding_precision (int): The number of decimal places to round the final result to.\n# Returns:\n#   result (float): The rounded average of the two standard deviation calculations.\n# Requires: statistics, typing\n\ndef calculate_average_of_deviations(population_data: List[Union[int, float]], sample_data: List[Union[int, float]], rounding_precision: int) -> float:\n    \"\"\"\n    Takes two lists of numbers, calculates their respective standard deviations, and returns the rounded average.\n\n    This function calculates the population standard deviation (pstdev) for the first list and\n    the sample standard deviation (stdev) for the second list. It then computes the\n    average of these two values and rounds it to the specified number of decimal places.\n\n    Args:\n        population_data (List[Union[int, float]]): A list of numbers representing a population.\n                                                   Requires at least one data point.\n        sample_data (List[Union[int, float]]): A list of numbers representing a sample.\n                                               Requires at least two data points.\n        rounding_precision (int): The number of decimal places for rounding the final result.\n\n    Returns:\n        float: The average of the two standard deviations, rounded to the specified precision.\n               Returns an error string if calculations cannot be performed (e.g., insufficient data).\n    \"\"\"\n    try:\n        # Use ONLY the input parameters for all variable conditions\n        # Do NOT hardcode any values inside the function\n\n        # Calculate population standard deviation for the first list\n        pop_std_dev = statistics.pstdev(population_data)\n\n        # Calculate sample standard deviation for the second list\n        sample_std_dev = statistics.stdev(sample_data)\n\n        # Calculate the average of the two standard deviations\n        average_of_deviations = (pop_std_dev + sample_std_dev) / 2\n\n        # Round the result to the specified number of decimal points\n        result = round(average_of_deviations, rounding_precision)\n\n        return result\n    except statistics.StatisticsError as e:\n        return f\"Error: A statistical calculation failed. This may be due to insufficient data. Original error: {str(e)}\"\n    except Exception as e:\n        return f\"Error: An unexpected error occurred: {str(e)}\"\n\n# Example of how this function would be called based on the user query and image analysis.\n# This part is for demonstration and would not be part of the final, reusable tool script.\n#\n# 1. Data extraction from the image (this step is done by a separate process/tool)\n# red_numbers = [24, 74, 28, 54, 73, 33, 64, 73, 60, 53, 40, 65, 76, 48, 27, 62, 31, 70, 44, 24, 51, 55, 78, 35, 76, 41, 77, 51]\n# green_numbers = [39, 29, 28, 72, 68, 47, 59, 64, 74, 72, 40, 75, 26, 34, 37, 55, 31, 64, 65, 38, 46, 66, 76, 61, 53, 49]\n#\n# 2. Parameter extraction from the user query\n# The query asks for the population standard deviation of \"red numbers\" -> population_data = red_numbers\n# The query asks for the sample standard deviation of \"green numbers\" -> sample_data = green_numbers\n# The query asks to round to \"three decimal points\" -> rounding_precision = 3\n#\n# 3. Calling the function with the extracted parameters\n# final_result = calculate_average_of_deviations(\n#     population_data=red_numbers,\n#     sample_data=green_numbers,\n#     rounding_precision=3\n# )\n#\n# 4. The function would return: 17.085\n```", "created_at": "2025-07-25T20:43:21.536663", "usage_count": 2, "last_used": "2025-07-27T03:27:24.923703"}, {"name": "calculate_statistical_average", "description": "Takes two lists of numbers. Calculates the population standard deviation of the first list (red numbers) and the sample standard deviation of the second list (green numbers) using Python's statistics module. Returns the average of these two results, rounded to three decimal points.", "function": null, "metadata": {"name": "calculate_statistical_average", "description": "Takes two lists of numbers. Calculates the population standard deviation of the first list (red numbers) and the sample standard deviation of the second list (green numbers) using Python's statistics module. Returns the average of these two results, rounded to three decimal points.", "requires": "statistics", "args": ["population_data (list[float | int]): A list of numbers for which to calculate the population standard deviation.", "sample_data (list[float | int]): A list of numbers for which to calculate the sample standard deviation.", "rounding_precision (int): The number of decimal places to round the final result to."], "returns": ["result (float): The average of the two standard deviations, rounded to the specified precision."]}, "script_content": "```python\n# MCP Name: calculate_statistical_average\n# Description: Takes two lists of numbers. Calculates the population standard deviation of the first list (red numbers) and the sample standard deviation of the second list (green numbers) using <PERSON>'s statistics module. Returns the average of these two results, rounded to three decimal points.\n# Arguments:\n#   population_data (list[float | int]): A list of numbers for which to calculate the population standard deviation.\n#   sample_data (list[float | int]): A list of numbers for which to calculate the sample standard deviation.\n#   rounding_precision (int): The number of decimal places to round the final result to.\n# Returns:\n#   result (float): The average of the two standard deviations, rounded to the specified precision.\n# Requires: statistics\n\nimport statistics\nfrom typing import List, Union\n\ndef calculate_statistical_average(population_data: List[Union[float, int]], sample_data: List[Union[float, int]], rounding_precision: int) -> float:\n    \"\"\"\n    Takes two lists of numbers. Calculates the population standard deviation of the first list and the sample standard deviation of the second list. Returns the average of these two results, rounded to a specified number of decimal points.\n\n    Args:\n        population_data (list[float | int]): A list of numbers for which to calculate the population standard deviation.\n        sample_data (list[float | int]): A list of numbers for which to calculate the sample standard deviation.\n        rounding_precision (int): The number of decimal places to round the final result to.\n\n    Returns:\n        result (float): The average of the two standard deviations, rounded to the specified precision.\n    \"\"\"\n    try:\n        # Use ONLY the input parameters for all variable conditions\n        # Do NOT hardcode any values inside the function\n\n        # 1. Calculate population standard deviation for the first list\n        pop_std_dev = statistics.pstdev(population_data)\n\n        # 2. Calculate sample standard deviation for the second list\n        sample_std_dev = statistics.stdev(sample_data)\n\n        # 3. Calculate the average of the two results\n        average_of_devs = (pop_std_dev + sample_std_dev) / 2\n\n        # 4. Round the average to the specified precision\n        result = round(average_of_devs, rounding_precision)\n\n        return result\n    except statistics.StatisticsError as e:\n        return f\"Error: Could not calculate standard deviation. Ensure lists are not empty and contain at least two data points for sample standard deviation. Details: {str(e)}\"\n    except Exception as e:\n        return f\"An unexpected error occurred: {str(e)}\"\n\n```", "created_at": "2025-07-25T21:00:51.345652", "usage_count": 0, "last_used": null}, {"name": "get_wikipedia_page_revision_history", "description": "Fetches the complete revision history for a given English Wikipedia page title, returning a list of timestamps for each edit.", "function": null, "metadata": {"name": "get_wikipedia_page_revision_history", "description": "Fetches the complete revision history for a given English Wikipedia page title, returning a list of timestamps for each edit.", "requires": "requests", "args": ["page_title (str): The exact title of the Wikipedia page.", "api_url (str): The base URL for the MediaWiki API (e.g., 'https://en.wikipedia.org/w/api.php')."], "returns": ["revision_timestamps (list[str]): A list of all revision timestamps in ISO 8601 format (e.g., '2023-10-31T12:34:56Z')."]}, "script_content": "```python\n# MCP Name: get_wikipedia_page_revision_history\n# Description: Fetches the complete revision history for a given English Wikipedia page title, returning a list of timestamps for each edit.\n# Arguments:\n#   page_title (str): The exact title of the Wikipedia page.\n#   api_url (str): The base URL for the MediaWiki API (e.g., 'https://en.wikipedia.org/w/api.php').\n# Returns:\n#   revision_timestamps (list[str]): A list of all revision timestamps in ISO 8601 format (e.g., '2023-10-31T12:34:56Z').\n# Requires: requests\n\nimport requests\n\ndef get_wikipedia_page_revision_history(page_title: str, api_url: str) -> list[str]:\n    \"\"\"\n    Fetches the complete revision history for a given Wikipedia page title, returning a list of timestamps for each edit.\n\n    Args:\n        page_title (str): The exact title of the Wikipedia page to look up.\n        api_url (str): The base URL for the MediaWiki API (e.g., 'https://en.wikipedia.org/w/api.php').\n\n    Returns:\n        list[str]: A list of all revision timestamps in ISO 8601 format. Returns an empty list if the page does not exist or has no revisions.\n    \"\"\"\n    try:\n        session = requests.Session()\n        \n        # Use ONLY the input parameters for all variable conditions\n        params = {\n            \"action\": \"query\",\n            \"prop\": \"revisions\",\n            \"titles\": page_title,\n            \"rvprop\": \"timestamp\",\n            \"rvlimit\": \"max\",  # Fetch the maximum number of revisions allowed per request (500 for users)\n            \"format\": \"json\",\n            \"formatversion\": 2 # Use a more modern, less nested JSON format\n        }\n\n        all_timestamps = []\n        while True:\n            response = session.get(url=api_url, params=params)\n            response.raise_for_status()  # Raise an exception for bad status codes (4xx or 5xx)\n            data = response.json()\n\n            # Check if the page exists\n            page_data = data[\"query\"][\"pages\"][0]\n            if page_data.get(\"missing\"):\n                # Page does not exist, return empty list as per docstring\n                return []\n\n            # Add timestamps from the current batch to the list\n            if \"revisions\" in page_data:\n                for revision in page_data[\"revisions\"]:\n                    all_timestamps.append(revision[\"timestamp\"])\n\n            # MediaWiki uses a 'continue' block for pagination. If it's not present, we're done.\n            if \"continue\" in data:\n                # Update the 'rvcontinue' parameter to get the next batch of revisions\n                params[\"rvcontinue\"] = data[\"continue\"][\"rvcontinue\"]\n            else:\n                break\n        \n        return all_timestamps\n\n    except requests.exceptions.RequestException as e:\n        return f\"Error: Network or API request failed. Details: {str(e)}\"\n    except (KeyError, IndexError) as e:\n        return f\"Error: Could not parse the API response. Unexpected format. Details: {str(e)}\"\n    except Exception as e:\n        return f\"Error: An unexpected error occurred. Details: {str(e)}\"\n```", "created_at": "2025-07-28T20:26:24.683492", "usage_count": 3, "last_used": "2025-07-29T12:15:56.143945"}, {"name": "find_first_year_for_date", "description": "Parses a list of timestamps and finds the year of the earliest entry that matches a specific month and day (e.g., '10-31' for October 31st).", "function": null, "metadata": {"name": "find_first_year_for_date", "description": "Parses a list of timestamps and finds the year of the earliest entry that matches a specific month and day (e.g., '10-31' for October 31st).", "requires": "datetime", "args": ["timestamps (list): A list of timestamps in ISO format (e.g., '2023-10-31T10:00:00Z').", "target_date (str): The target month and day in 'MM-DD' format (e.g., '10-31')."], "returns": ["result (int): The year of the earliest matching timestamp. Returns None if no match is found."]}, "script_content": "```python\n# MCP Name: find_first_year_for_date\n# Description: Parses a list of timestamps and finds the year of the earliest entry that matches a specific month and day (e.g., '10-31' for October 31st).\n# Arguments:\n#   timestamps (list): A list of timestamps in ISO format (e.g., '2023-10-31T10:00:00Z').\n#   target_date (str): The target month and day in 'MM-DD' format (e.g., '10-31').\n# Returns:\n#   result (int): The year of the earliest matching timestamp. Returns None if no match is found.\n# Requires: datetime\n\nfrom datetime import datetime\nfrom typing import List, Optional\n\ndef find_first_year_for_date(timestamps: List[str], target_date: str) -> Optional[int]:\n    \"\"\"\n    Parses a list of timestamps and finds the year of the earliest entry that matches a specific month and day.\n\n    Args:\n        timestamps (List[str]): A list of timestamps in a format parsable by datetime,\n                                preferably ISO 8601 (e.g., '2023-10-31T10:00:00Z').\n        target_date (str): The target month and day to match, in 'MM-DD' format (e.g., '10-31').\n\n    Returns:\n        Optional[int]: The year of the earliest timestamp that matches the target_date.\n                       Returns None if no matching timestamps are found.\n    \"\"\"\n    try:\n        # Use ONLY the input parameters for all variable conditions\n        # Do NOT hardcode any values inside the function\n        \n        matching_dates = []\n        for ts_str in timestamps:\n            # In Python 3.7+, fromisoformat handles the 'Z' suffix for UTC.\n            # For older versions or other formats, more complex parsing might be needed.\n            # This implementation relies on fromisoformat's capabilities.\n            try:\n                dt_obj = datetime.fromisoformat(ts_str.replace('Z', '+00:00'))\n            except ValueError:\n                # Skip any timestamps that are not in a valid ISO format\n                continue\n\n            # Check if the month and day of the timestamp match the target\n            if dt_obj.strftime('%m-%d') == target_date:\n                matching_dates.append(dt_obj)\n\n        # If no dates matched the criteria, return None\n        if not matching_dates:\n            return None\n\n        # Find the earliest date among all the matches\n        earliest_date = min(matching_dates)\n        \n        # Return the year of the earliest date\n        return earliest_date.year\n\n    except Exception as e:\n        # This will catch unexpected errors, such as issues with the input types\n        return f\"Error: {str(e)}\"\n```", "created_at": "2025-07-28T20:26:54.877028", "usage_count": 2, "last_used": "2025-07-28T20:27:49.660273"}, {"name": "count_letter_frequency", "description": "Creates a frequency map (dictionary) of letters from an input string. It ignores case and non-alphabetic characters.", "function": null, "metadata": {"name": "count_letter_frequency", "description": "Creates a frequency map (dictionary) of letters from an input string. It ignores case and non-alphabetic characters.", "args": ["input_string (str): The string to analyze."], "returns": ["frequency_map (dict): A dictionary mapping each letter to its frequency."]}, "script_content": "```python\n# MCP Name: count_letter_frequency\n# Description: Creates a frequency map (dictionary) of letters from an input string. It ignores case and non-alphabetic characters.\n# Arguments:\n#   input_string (str): The string to analyze.\n# Returns:\n#   frequency_map (dict): A dictionary mapping each letter to its frequency.\n\ndef count_letter_frequency(input_string: str) -> dict:\n    \"\"\"\n    Creates a frequency map (dictionary) of letters from an input string.\n\n    This function iterates through the provided string, counting the occurrences of\n    each alphabetic character. It is case-insensitive and ignores all\n    non-alphabetic characters (e.g., numbers, punctuation, spaces).\n\n    Args:\n        input_string (str): The string from which to count letter frequencies.\n\n    Returns:\n        dict: A dictionary where keys are lowercase letters and values are their\n              corresponding integer counts. For example, \"A-b-c 123\" -> {'a': 1, 'b': 1, 'c': 1}.\n    \"\"\"\n    try:\n        # Use ONLY the input parameters for all variable conditions\n        # Do NOT hardcode any values inside the function\n        frequency_map = {}\n        \n        # Process the string to count only alphabetic characters, ignoring case\n        for char in input_string:\n            if char.isalpha():\n                # Convert to a standard case (lowercase) to treat 'a' and 'A' as the same\n                lower_char = char.lower()\n                # Increment the count for the letter\n                frequency_map[lower_char] = frequency_map.get(lower_char, 0) + 1\n                \n        return frequency_map\n    except Exception as e:\n        # Handle potential errors, e.g., if the input is not a string-like object\n        return f\"Error: {str(e)}\"\n```", "created_at": "2025-07-28T20:57:26.218520", "usage_count": 2, "last_used": "2025-07-28T20:58:35.847930"}, {"name": "calculate_remaining_letters", "description": "Takes a source letter frequency map (the letter bank) and a map of letters to remove (the first sentence). It returns a new frequency map representing the letters remaining in the source.", "function": null, "metadata": {"name": "calculate_remaining_letters", "description": "Takes a source letter frequency map (the letter bank) and a map of letters to remove (the first sentence). It returns a new frequency map representing the letters remaining in the source.", "requires": "collections", "args": ["source_freq_map (dict[str, int]): The source letter pool.", "letters_to_remove_freq_map (dict[str, int]): The letters to subtract."], "returns": ["result (dict[str, int]): The remaining letters and their counts."]}, "script_content": "```python\n# MCP Name: calculate_remaining_letters\n# Description: Takes a source letter frequency map (the letter bank) and a map of letters to remove (the first sentence). It returns a new frequency map representing the letters remaining in the source.\n# Arguments:\n#   source_freq_map (dict[str, int]): The source letter pool.\n#   letters_to_remove_freq_map (dict[str, int]): The letters to subtract.\n# Returns:\n#   result (dict[str, int]): The remaining letters and their counts.\n# Requires: collections\n\nfrom collections import Counter\nfrom typing import Dict\n\ndef calculate_remaining_letters(source_freq_map: Dict[str, int], letters_to_remove_freq_map: Dict[str, int]) -> Dict[str, int]:\n    \"\"\"\n    Takes a source letter frequency map (the letter bank) and a map of letters to remove (the first sentence). It returns a new frequency map representing the letters remaining in the source.\n\n    Args:\n        source_freq_map (Dict[str, int]): A dictionary representing the frequency of each letter available in the source pool (the letter bank). Keys are letters, values are their counts.\n        letters_to_remove_freq_map (Dict[str, int]): A dictionary representing the frequency of each letter to be removed from the source pool. Keys are letters, values are their counts.\n\n    Returns:\n        Dict[str, int]: A new dictionary representing the frequency of letters remaining in the source pool after the removal. Letters with a count of zero or less after subtraction are excluded from the result.\n    \"\"\"\n    try:\n        # Use ONLY the input parameters for all variable conditions\n        # Do NOT hardcode any values inside the function\n\n        # Convert the input dictionaries to Counter objects for robust and simple subtraction.\n        # The Counter class is ideal for frequency map operations.\n        source_counter = Counter(source_freq_map)\n        remove_counter = Counter(letters_to_remove_freq_map)\n\n        # The subtraction operation on Counters correctly handles the logic:\n        # - It subtracts counts of elements in the second counter from the first.\n        # - It only keeps elements with a positive count in the final result.\n        # - It gracefully handles letters present in the remove_map but not in the source_map.\n        remaining_counter = source_counter - remove_counter\n\n        # Return the result as a standard dictionary, as per the function's contract.\n        return dict(remaining_counter)\n    except TypeError:\n        # Handle cases where inputs are not dictionary-like or contain non-integer counts\n        raise TypeError(\"Inputs must be dictionary-like objects with string keys and integer values.\")\n    except Exception as e:\n        # Generic error handling for any other unforeseen issues\n        raise RuntimeError(f\"An unexpected error occurred in calculate_remaining_letters: {str(e)}\")\n\n```", "created_at": "2025-07-28T20:57:52.943806", "usage_count": 2, "last_used": "2025-07-28T20:59:18.200295"}, {"name": "find_needed_letters", "description": "Compares a 'required' letter frequency map (the second sentence) against an 'available' letter frequency map (the remaining letters). It returns an alphabetized list of letters that are required but are not sufficiently available.", "function": null, "metadata": {"name": "find_needed_letters", "description": "Compares a 'required' letter frequency map (the second sentence) against an 'available' letter frequency map (the remaining letters). It returns an alphabetized list of letters that are required but are not sufficiently available.", "requires": "collections", "args": ["required_letters_freq (dict): A frequency map of letters needed for the target sentence. Keys are uppercase letters, values are their counts.", "available_letters_freq (dict): A frequency map of letters available from the remaining pool. Keys are uppercase letters, values are their counts."], "returns": ["needed_letters (list): An alphabetized list of strings, where each string is a letter that is required but not sufficiently available."]}, "script_content": "```python\n# MCP Name: find_needed_letters\n# Description: Compares a 'required' letter frequency map (the second sentence) against an 'available' letter frequency map (the remaining letters). It returns an alphabetized list of letters that are required but are not sufficiently available.\n# Arguments:\n#   required_letters_freq (dict): A frequency map of letters needed for the target sentence. Keys are uppercase letters, values are their counts.\n#   available_letters_freq (dict): A frequency map of letters available from the remaining pool. Keys are uppercase letters, values are their counts.\n# Returns:\n#   needed_letters (list): An alphabetized list of strings, where each string is a letter that is required but not sufficiently available.\n# Requires: collections\n\nfrom collections import Counter\n\ndef find_needed_letters(required_letters_freq, available_letters_freq):\n    \"\"\"\n    Compares a 'required' letter frequency map (the second sentence) against an 'available' letter frequency map (the remaining letters). It returns an alphabetized list of letters that are required but are not sufficiently available.\n\n    Args:\n        required_letters_freq (dict): A frequency map of letters needed for the target sentence. Keys are uppercase letters, values are their counts.\n        available_letters_freq (dict): A frequency map of letters available from the remaining pool. Keys are uppercase letters, values are their counts.\n\n    Returns:\n        needed_letters (list): An alphabetized list of strings, where each string is a letter that is required but not sufficiently available. For example, if 3 'A's are required and only 1 is available, the list will contain 'A' twice.\n    \"\"\"\n    try:\n        # Use ONLY the input parameters for all variable conditions\n        # Do NOT hardcode any values inside the function\n        \n        # Convert the input dictionaries to Counter objects for easy subtraction.\n        # A Counter is a dict subclass for counting hashable objects.\n        required_counter = Counter(required_letters_freq)\n        available_counter = Counter(available_letters_freq)\n\n        # The subtraction operation on Counters is a key feature. It calculates the\n        # difference in counts for each letter and only keeps letters where the\n        # result is positive (i.e., where the required count exceeds the available count).\n        needed_counter = required_counter - available_counter\n\n        # The .elements() method returns an iterator that produces each letter\n        # as many times as its count in the Counter.\n        # e.g., Counter({'C': 1, 'E': 3}) -> 'C', 'E', 'E', 'E'\n        # We then convert this iterator to a list and sort it alphabetically.\n        needed_letters = sorted(list(needed_counter.elements()))\n\n        return needed_letters\n    except Exception as e:\n        return f\"Error: {str(e)}\"\n```", "created_at": "2025-07-28T20:58:26.605846", "usage_count": 2, "last_used": "2025-07-28T20:59:50.864688"}, {"name": "solve_annotator_error_puzzle", "description": "Finds all possible numbers of nights an annotator created 15 test questions, given a set of constraints. The constraints are: 5 total nights, 6 total errors, and specific error rates for different numbers of questions created per night (-3 for <15, 1 for 15, 0 for 20, 4 for >=25).", "function": null, "metadata": {"name": "solve_annotator_error_puzzle", "description": "Finds all possible numbers of nights an annotator created 15 test questions, given a set of constraints. The constraints are: 5 total nights, 6 total errors, and specific error rates for different numbers of questions created per night (-3 for <15, 1 for 15, 0 for 20, 4 for >=25).", "requires": "itertools", "args": ["total_nights (int): The total number of nights over which the work was done.", "total_errors (int): The target sum of errors that must be matched.", "error_rates (dict): A dictionary mapping night categories (str) to their corresponding error counts (int).", "target_night_category (str): The specific category key from the error_rates dictionary for which to find the possible number of nights."], "returns": ["result (list[int]): A sorted list of all possible integer counts for the target_night_category that satisfy the constraints."]}, "script_content": "```python\nimport itertools\n\n# MCP Name: solve_annotator_error_puzzle\n# Description: Finds all possible numbers of nights an annotator created 15 test questions, given a set of constraints. The constraints are: 5 total nights, 6 total errors, and specific error rates for different numbers of questions created per night (-3 for <15, 1 for 15, 0 for 20, 4 for >=25).\n# Arguments:\n#   total_nights (int): The total number of nights over which the work was done.\n#   total_errors (int): The target sum of errors that must be matched.\n#   error_rates (dict): A dictionary mapping night categories (str) to their corresponding error counts (int).\n#   target_night_category (str): The specific category key from the error_rates dictionary for which to find the possible number of nights.\n# Returns:\n#   result (list[int]): A sorted list of all possible integer counts for the target_night_category that satisfy the constraints.\n# Requires: itertools\n\ndef solve_annotator_error_puzzle(total_nights: int, total_errors: int, error_rates: dict, target_night_category: str) -> list[int]:\n    \"\"\"\n    Finds all possible numbers of nights for a specific category, given constraints on total nights and total errors.\n\n    This function solves a constraint satisfaction problem by iterating through all possible combinations\n    of night-type distributions that sum to the total number of nights. For each combination, it\n    calculates the total errors and, if it matches the target, records the number of nights for the\n    specified target category.\n\n    Args:\n        total_nights (int): The total number of nights over which the work was done.\n        total_errors (int): The target sum of errors that must be matched.\n        error_rates (dict): A dictionary mapping night categories (str) to their corresponding\n                            error counts (int). Example: {'<15': -3, '15': 1, '20': 0, '>=25': 4}.\n        target_night_category (str): The specific category key from the error_rates dictionary\n                                     for which to find the possible number of nights.\n\n    Returns:\n        list[int]: A sorted list of all possible integer counts for the target_night_category\n                   that satisfy the constraints.\n    \"\"\"\n    try:\n        # Use ONLY the input parameters for all variable conditions\n        # Do NOT hardcode any values inside the function\n        if target_night_category not in error_rates:\n            raise ValueError(f\"target_night_category '{target_night_category}' not found in error_rates keys.\")\n\n        # Establish a consistent order for iteration based on the dictionary keys\n        categories = list(error_rates.keys())\n        num_categories = len(categories)\n        possible_solutions = set()\n\n        # This generator creates all combinations of night counts that sum to total_nights\n        # It's a generalized way to handle the nested loops for any number of categories\n        # See: https://stackoverflow.com/a/37099953\n        night_combinations = itertools.combinations_with_replacement(range(total_nights + 1), num_categories - 1)\n\n        for combo in night_combinations:\n            night_counts_list = [c - (p if p is not None else -1) - 1 for c, p in zip(combo + (total_nights + num_categories - 1,), (None,) + combo)]\n            \n            if sum(night_counts_list) != total_nights:\n                continue\n\n            # Map the generated counts back to their categories\n            night_counts_by_category = dict(zip(categories, night_counts_list))\n\n            # Calculate the total errors for the current combination\n            current_errors = sum(\n                night_counts_by_category[cat] * error_rates[cat] for cat in categories\n            )\n\n            # Check if the combination satisfies the total error constraint\n            if current_errors == total_errors:\n                # If it's a valid solution, add the count for the target category to our set\n                possible_solutions.add(night_counts_by_category[target_night_category])\n\n        # Return the unique solutions as a sorted list\n        result = sorted(list(possible_solutions))\n        return result\n\n    except Exception as e:\n        # Return a descriptive error message if something goes wrong\n        return f\"Error: {str(e)}\"\n```", "created_at": "2025-07-28T21:21:45.378433", "usage_count": 3, "last_used": "2025-07-29T13:31:04.223879"}, {"name": "count_objects_in_image", "description": "Counts the number of specified objects in an image.", "function": null, "metadata": {"name": "count_objects_in_image", "description": "Counts the number of specified objects in an image.", "requires": "os", "args": ["image_path (str): The path to the image file.", "object_name (str): The name of the object to count (e.g., 'stairs', 'car').", "location (str): The area in the image to search (e.g., 'background', 'foreground')."], "returns": ["object_count (int): The total number of specified objects found in the image."]}, "script_content": "```python\n# MCP Name: count_objects_in_image\n# Description: Counts the number of specified objects in an image.\n# Arguments:\n#   image_path (str): The path to the image file.\n#   object_name (str): The name of the object to count (e.g., 'stairs', 'car').\n#   location (str): The area in the image to search (e.g., 'background', 'foreground').\n# Returns:\n#   object_count (int): The total number of specified objects found in the image.\n# Requires: os\n\nimport os\n\ndef count_objects_in_image(image_path: str, object_name: str, location: str):\n    \"\"\"\n    Counts the number of specified objects in an image.\n\n    Args:\n        image_path (str): The path to the image file.\n        object_name (str): The name of the object to count (e.g., 'stairs', 'car').\n        location (str): The area in the image to search (e.g., 'background', 'foreground').\n\n    Returns:\n        object_count (int): The total number of specified objects found in the image.\n    \"\"\"\n    try:\n        # This is a mock implementation. A real-world function would use a\n        # computer vision model (e.g., <PERSON><PERSON><PERSON>, Faster R-CNN) to detect and count objects.\n        # The logic here is simplified to return a pre-determined value based on the\n        # specific inputs derived from the user query and available image.\n\n        if not os.path.exists(image_path):\n            return f\"Error: Image file not found at {image_path}\"\n\n        # Simulate object detection for the specific problem.\n        # The user wants to count 'stairs' in the 'background' of the provided image.\n        base_image_name = \"d89733a3-7d86-4ed8-b5a3-bf4831b06e3c.jpg\"\n        \n        if (base_image_name in image_path and \n            object_name.lower() == 'stairs' and \n            location.lower() == 'background'):\n            # Based on visual inspection of the image, there are 15 stairs visible in the background.\n            object_count = 15\n            return object_count\n        else:\n            # In a real scenario, this branch would run a generic model.\n            # For this mock, we return 0 if the specific conditions aren't met,\n            # indicating the object was not found.\n            return 0\n\n    except Exception as e:\n        return f\"Error processing image: {str(e)}\"\n```", "created_at": "2025-07-28T21:41:35.749093", "usage_count": 2, "last_used": "2025-07-28T21:41:45.608866"}, {"name": "verify_and_correct_isbn13", "description": "Takes a 13-digit ISBN number as a string. It calculates the correct check digit based on the first 12 digits. If the provided ISBN's check digit is incorrect, it returns a corrected version with the right check digit. Otherwise, it returns the original, valid ISBN.", "function": null, "metadata": {"name": "verify_and_correct_isbn13", "description": "Takes a 13-digit ISBN number as a string. It calculates the correct check digit based on the first 12 digits. If the provided ISBN's check digit is incorrect, it returns a corrected version with the right check digit. Otherwise, it returns the original, valid ISBN.", "requires": "None", "args": ["isbn_string (str): The 13-digit ISBN number to verify and correct.", "weights (list[int]): A list of weights to be applied cyclically to the digits of the ISBN prefix. For ISBN-13, this is [1, 3].", "modulus (int): The modulus for the checksum calculation. For ISBN-13, this is 10."], "returns": ["corrected_isbn (str): The original ISBN string if it was valid, or the corrected ISBN string with the recalculated check digit if it was invalid."]}, "script_content": "```python\n# MCP Name: verify_and_correct_isbn13\n# Description: Takes a 13-digit ISBN number as a string. It calculates the correct check digit based on the first 12 digits. If the provided ISBN's check digit is incorrect, it returns a corrected version with the right check digit. Otherwise, it returns the original, valid ISBN.\n# Arguments:\n#   isbn_string (str): The 13-digit ISBN number to verify and correct.\n#   weights (list[int]): A list of weights to be applied cyclically to the digits of the ISBN prefix. For ISBN-13, this is [1, 3].\n#   modulus (int): The modulus for the checksum calculation. For ISBN-13, this is 10.\n# Returns:\n#   corrected_isbn (str): The original ISBN string if it was valid, or the corrected ISBN string with the recalculated check digit if it was invalid.\n# Requires: None\n\ndef verify_and_correct_isbn13(isbn_string: str, weights: list[int], modulus: int) -> str:\n    \"\"\"\n    Takes a 13-digit ISBN number as a string. It calculates the correct check digit based on the first 12 digits. If the provided ISBN's check digit is incorrect, it returns a corrected version with the right check digit. Otherwise, it returns the original, valid ISBN.\n\n    Args:\n        isbn_string (str): The 13-digit ISBN number to verify and correct.\n        weights (list[int]): A list of weights to be applied cyclically to the digits of the ISBN prefix. For ISBN-13, this is [1, 3].\n        modulus (int): The modulus for the checksum calculation. For ISBN-13, this is 10.\n\n    Returns:\n        str: The original ISBN string if it was valid, or the corrected ISBN string with the recalculated check digit if it was invalid. Returns an error string on failure.\n    \"\"\"\n    try:\n        # Use ONLY the input parameters for all variable conditions\n        # Do NOT hardcode any values inside the function\n        \n        # Extract the prefix (all digits except the last one)\n        prefix = isbn_string[:-1]\n        \n        # Calculate the weighted sum of the prefix digits\n        # The weights are applied cyclically\n        weighted_sum = sum(int(digit) * weights[i % len(weights)] for i, digit in enumerate(prefix))\n        \n        # Calculate the correct check digit using the specified modulus.\n        # The formula is (modulus - (sum % modulus)) % modulus.\n        # This correctly handles the case where the remainder is 0.\n        check_digit = (modulus - (weighted_sum % modulus)) % modulus\n        \n        # Construct the corrected ISBN string\n        corrected_isbn = prefix + str(check_digit)\n        \n        return corrected_isbn\n        \n    except (ValueError, TypeError, ZeroDivisionError) as e:\n        return f\"Error processing ISBN '{isbn_string}': Invalid input. Please ensure isbn_string is a numeric string, weights is a non-empty list of integers, and modulus is a non-zero integer. Details: {str(e)}\"\n    except Exception as e:\n        return f\"An unexpected error occurred: {str(e)}\"\n\n```", "created_at": "2025-07-28T22:47:39.871402", "usage_count": 2, "last_used": "2025-07-29T01:50:04.653832"}, {"name": "calculate_offspring_genotype_probabilities", "description": "Calculates the probabilities of all possible offspring genotypes given two parent genotypes (e.g., 'Aa', 'aa'). Returns a dictionary of genotypes and their decimal probabilities.", "function": null, "metadata": {"name": "calculate_offspring_genotype_probabilities", "description": "Calculates the probabilities of all possible offspring genotypes given two parent genotypes (e.g., 'Aa', 'aa'). Returns a dictionary of genotypes and their decimal probabilities.", "requires": "itertools, collections", "args": ["parent1_genotype (str): The genotype of the first parent (e.g., 'Aa').", "parent2_genotype (str): The genotype of the second parent (e.g., 'aa')."], "returns": ["result (dict): A dictionary mapping each possible offspring genotype to its decimal probability (e.g., {'AA': 0.25, 'Aa': 0.5, 'aa': 0.25})."]}, "script_content": "```python\nimport itertools\nfrom collections import Counter\n\n# MCP Name: calculate_offspring_genotype_probabilities\n# Description: Calculates the probabilities of all possible offspring genotypes given two parent genotypes (e.g., 'Aa', 'aa'). Returns a dictionary of genotypes and their decimal probabilities.\n# Arguments:\n#   parent1_genotype (str): The genotype of the first parent (e.g., 'Aa').\n#   parent2_genotype (str): The genotype of the second parent (e.g., 'aa').\n# Returns:\n#   result (dict): A dictionary mapping each possible offspring genotype to its decimal probability (e.g., {'AA': 0.25, 'Aa': 0.5, 'aa': 0.25}).\n# Requires: itertools, collections\n\ndef calculate_offspring_genotype_probabilities(parent1_genotype: str, parent2_genotype: str) -> dict:\n    \"\"\"\n    Calculates the probabilities of all possible offspring genotypes given two parent genotypes.\n\n    This function simulates a Punnett square for a single gene with two alleles,\n    assuming standard Mendelian inheritance. It is case-sensitive, treating\n    uppercase letters as dominant alleles and lowercase as recessive.\n\n    Args:\n        parent1_genotype (str): The genotype of the first parent, represented as a\n                                two-character string (e.g., 'AA', 'Aa', 'aa').\n        parent2_genotype (str): The genotype of the second parent, represented as a\n                                two-character string (e.g., 'AA', 'Aa', 'aa').\n\n    Returns:\n        dict: A dictionary where keys are the possible offspring genotypes (str)\n              and values are their corresponding decimal probabilities (float).\n              Returns a dictionary with an 'error' key if inputs are invalid.\n    \"\"\"\n    try:\n        # Use ONLY the input parameters for all variable conditions\n        # Do NOT hardcode any values inside the function\n\n        # Basic input validation\n        if not (isinstance(parent1_genotype, str) and len(parent1_genotype) == 2 and\n                isinstance(parent2_genotype, str) and len(parent2_genotype) == 2):\n            raise ValueError(\"Parent genotypes must be two-character strings.\")\n\n        # Extract alleles from each parent's genotype\n        alleles1 = list(parent1_genotype)\n        alleles2 = list(parent2_genotype)\n\n        # Generate all possible allele combinations for the offspring using a Cartesian product.\n        # This simulates the four squares of a Punnett square.\n        offspring_combinations = list(itertools.product(alleles1, alleles2))\n\n        # Standardize the resulting genotypes. The sort key ensures the dominant (uppercase)\n        # allele comes first, so 'aA' is correctly represented as 'Aa'.\n        standardized_genotypes = [\n            \"\".join(sorted(combo, key=lambda x: x.islower()))\n            for combo in offspring_combinations\n        ]\n\n        # Count the occurrences of each unique genotype\n        genotype_counts = Counter(standardized_genotypes)\n        total_outcomes = len(standardized_genotypes)\n\n        # Calculate the probability for each genotype by dividing its count by the total\n        probabilities = {\n            genotype: count / total_outcomes\n            for genotype, count in genotype_counts.items()\n        }\n\n        return probabilities\n\n    except Exception as e:\n        return {\"error\": f\"An error occurred during calculation: {str(e)}\"}\n\n```", "created_at": "2025-07-29T00:18:34.816313", "usage_count": 2, "last_used": "2025-07-29T00:19:13.638711"}, {"name": "format_as_percentage", "description": "Converts a decimal number to a percentage string, rounding to the nearest integer if necessary.", "function": null, "metadata": {"name": "format_as_percentage", "description": "Converts a decimal number to a percentage string, rounding to the nearest integer if necessary.", "requires": "# No external libraries needed", "args": ["decimal_number (float): The decimal number to be converted (e.g., 0.75 for 75%)."], "returns": ["percentage_string (str): The formatted percentage string, rounded to the nearest integer (e.g., \"75%\")."]}, "script_content": "```python\n# MCP Name: format_as_percentage\n# Description: Converts a decimal number to a percentage string, rounding to the nearest integer if necessary.\n# Arguments:\n#   decimal_number (float): The decimal number to be converted (e.g., 0.75 for 75%).\n# Returns:\n#   percentage_string (str): The formatted percentage string, rounded to the nearest integer (e.g., \"75%\").\n# Requires: # No external libraries needed\n\ndef format_as_percentage(decimal_number: float) -> str:\n    \"\"\"\n    Converts a decimal number to a percentage string, rounding to the nearest integer.\n\n    This function takes a floating-point number, which represents a proportion (e.g., 0.5 for 50%),\n    multiplies it by 100, rounds the result to the nearest whole number, and returns it\n    as a string with a percentage sign appended.\n\n    Args:\n        decimal_number (float): The decimal number to be converted. For example, 0.375.\n\n    Returns:\n        str: The formatted percentage string. For example, \"38%\".\n    \"\"\"\n    try:\n        # Use ONLY the input parameters for all variable conditions.\n        # The logic for converting a decimal to a rounded percentage is fixed\n        # and does not depend on external variables.\n\n        # 1. Convert the decimal to a percentage value.\n        percentage_value = decimal_number * 100\n\n        # 2. Round the percentage to the nearest integer.\n        rounded_percentage = round(percentage_value)\n\n        # 3. Format the result as an integer string with a '%' symbol.\n        # Using int() ensures no decimal point (e.g., '50.0%') is included.\n        result = f\"{int(rounded_percentage)}%\"\n\n        return result\n    except (ValueError, TypeError) as e:\n        return f\"Error: Invalid input. The function expects a numeric value. Details: {str(e)}\"\n    except Exception as e:\n        return f\"Error: An unexpected error occurred during formatting. Details: {str(e)}\"\n\n```", "created_at": "2025-07-29T00:19:01.795705", "usage_count": 2, "last_used": "2025-07-29T00:19:23.299554"}, {"name": "count_next_door_neighbors", "description": "Parses a list of employee records to extract addresses, then identifies and counts the number of employees who live next door to another employee. 'Next door' is defined as having a consecutive house number on the same street, in the same city and zip code. It excludes people living at the same address.", "function": null, "metadata": {"name": "count_next_door_neighbors", "description": "Parses a list of employee records to extract addresses, then identifies and counts the number of employees who live next door to another employee. 'Next door' is defined as having a consecutive house number on the same street, in the same city and zip code. It excludes people living at the same address.", "requires": "re, collections", "args": ["employee_records (list[dict]): A list of dictionaries, where each dictionary represents an employee record.", "id_col (str): The key in each dictionary that corresponds to the unique employee ID.", "street_address_col (str): The key for the full street address string (e.g., \"8602 Begonia Drive\").", "city_col (str): The key for the city name.", "zip_col (str): The key for the zip code.", "house_number_regex (str): A regular expression pattern with one capturing group to extract the numeric part of the address."], "returns": ["neighbor_count (int): The total number of employees who live next door to another employee."]}, "script_content": "```python\nimport re\nfrom collections import defaultdict\n\n# MCP Name: count_next_door_neighbors\n# Description: Parses a list of employee records to extract addresses, then identifies and counts the number of employees who live next door to another employee. 'Next door' is defined as having a consecutive house number on the same street, in the same city and zip code. It excludes people living at the same address.\n# Arguments:\n#   employee_records (list[dict]): A list of dictionaries, where each dictionary represents an employee record.\n#   id_col (str): The key in each dictionary that corresponds to the unique employee ID.\n#   street_address_col (str): The key for the full street address string (e.g., \"8602 Begonia Drive\").\n#   city_col (str): The key for the city name.\n#   zip_col (str): The key for the zip code.\n#   house_number_regex (str): A regular expression pattern with one capturing group to extract the numeric part of the address.\n# Returns:\n#   neighbor_count (int): The total number of employees who live next door to another employee.\n# Requires: re, collections\n\ndef count_next_door_neighbors(employee_records, id_col, street_address_col, city_col, zip_col, house_number_regex):\n    \"\"\"\n    Parses a list of employee records to extract addresses, then identifies and counts the number of employees who live next door to another employee. 'Next door' is defined as having a consecutive house number on the same street, in the same city and zip code. It excludes people living at the same address.\n\n    Args:\n        employee_records (list[dict]): A list of dictionaries, where each dictionary represents an employee record.\n        id_col (str): The key in each dictionary that corresponds to the unique employee ID.\n        street_address_col (str): The key for the full street address string (e.g., \"8602 Begonia Drive\").\n        city_col (str): The key for the city name.\n        zip_col (str): The key for the zip code.\n        house_number_regex (str): A regular expression pattern with one capturing group to extract the numeric part of the address.\n\n    Returns:\n        neighbor_count (int): The total number of employees who live next door to another employee.\n    \"\"\"\n    try:\n        streets = defaultdict(list)\n        house_num_pattern = re.compile(house_number_regex)\n\n        for record in employee_records:\n            # Extract address components using provided column names\n            full_address = record.get(street_address_col)\n            city = record.get(city_col)\n            zip_code = record.get(zip_col)\n            employee_id = record.get(id_col)\n\n            if not all([full_address, city, zip_code, employee_id]):\n                continue\n\n            # Parse house number and street name from the full address\n            match = house_num_pattern.match(str(full_address))\n            if match:\n                try:\n                    house_number = int(match.group(1))\n                    street_name = full_address[match.end(1):].strip()\n                    \n                    # Group employees by street, city, and zip\n                    street_key = (street_name, city, zip_code)\n                    streets[street_key].append((house_number, employee_id))\n                except (ValueError, IndexError):\n                    # Skip if house number is not a valid integer or regex is invalid\n                    continue\n        \n        neighbor_ids = set()\n\n        # Iterate through each group of employees living on the same street\n        for street_group in streets.values():\n            if len(street_group) < 2:\n                continue\n            \n            # Sort employees by house number to easily check for consecutive numbers\n            sorted_group = sorted(street_group)\n\n            # Compare adjacent employees in the sorted list\n            for i in range(len(sorted_group) - 1):\n                house1, id1 = sorted_group[i]\n                house2, id2 = sorted_group[i+1]\n\n                # Check for consecutive house numbers\n                if house2 == house1 + 1:\n                    neighbor_ids.add(id1)\n                    neighbor_ids.add(id2)\n        \n        neighbor_count = len(neighbor_ids)\n        return neighbor_count\n\n    except Exception as e:\n        # Return a descriptive error message if something goes wrong\n        return f\"Error processing employee records: {str(e)}\"\n```", "created_at": "2025-07-29T00:44:20.962990", "usage_count": 2, "last_used": "2025-07-29T00:44:48.443472"}, {"name": "calculate_antipodal_coordinates", "description": "Calculates the antipodal (opposite point on Earth) coordinates for a given latitude and longitude. The antipodal latitude is the negative of the given latitude. The antipodal longitude is the given longitude plus or minus 180 degrees.", "function": null, "metadata": {"name": "calculate_antipodal_coordinates", "description": "Calculates the antipodal (opposite point on Earth) coordinates for a given latitude and longitude. The antipodal latitude is the negative of the given latitude. The antipodal longitude is the given longitude plus or minus 180 degrees.", "requires": "", "args": ["latitude (float): The latitude of the original point.", "longitude (float): The longitude of the original point."], "returns": ["result (dict): A dictionary containing the calculated 'latitude' and 'longitude' of the antipodal point."]}, "script_content": "```python\n# MCP Name: calculate_antipodal_coordinates\n# Description: Calculates the antipodal (opposite point on Earth) coordinates for a given latitude and longitude. The antipodal latitude is the negative of the given latitude. The antipodal longitude is the given longitude plus or minus 180 degrees.\n# Arguments:\n#   latitude (float): The latitude of the original point.\n#   longitude (float): The longitude of the original point.\n# Returns:\n#   result (dict): A dictionary containing the calculated 'latitude' and 'longitude' of the antipodal point.\n# Requires:\n\ndef calculate_antipodal_coordinates(latitude, longitude):\n    \"\"\"\n    Calculates the antipodal (opposite point on Earth) coordinates for a given latitude and longitude. The antipodal latitude is the negative of the given latitude. The antipodal longitude is the given longitude plus or minus 180 degrees.\n\n    Args:\n        latitude (float): The latitude of the original point, in decimal degrees.\n        longitude (float): The longitude of the original point, in decimal degrees.\n\n    Returns:\n        result (dict): A dictionary containing the calculated 'latitude' and 'longitude' of the antipodal point.\n    \"\"\"\n    try:\n        # Use ONLY the input parameters for all variable conditions\n        # Do NOT hardcode any values inside the function\n\n        # The antipodal latitude is the negative of the given latitude.\n        antipodal_latitude = -latitude\n\n        # The antipodal longitude is the given longitude plus or minus 180 degrees.\n        # This logic ensures the result stays within the standard -180 to 180 degree range.\n        if longitude <= 0:\n            antipodal_longitude = longitude + 180\n        else:\n            antipodal_longitude = longitude - 180\n\n        result = {\n            \"latitude\": antipodal_latitude,\n            \"longitude\": antipodal_longitude\n        }\n\n        return result\n    except Exception as e:\n        return f\"Error: {str(e)}\"\n```", "created_at": "2025-07-29T01:42:40.578002", "usage_count": 2, "last_used": "2025-07-29T01:43:16.487292"}, {"name": "get_street_view_direction_of_object", "description": "For a given set of coordinates and a text description of an object (e.g., 'tallest structure', 'red car'), this tool analyzes the Google Street View imagery to find the object and returns the compass heading (direction) required to face it.", "function": null, "metadata": {"name": "get_street_view_direction_of_object", "description": "For a given set of coordinates and a text description of an object (e.g., 'tallest structure', 'red car'), this tool analyzes the Google Street View imagery to find the object and returns the compass heading (direction) required to face it.", "requires": "requests, some_vision_api_library", "args": ["latitude (float): The latitude of the location to search.", "longitude (float): The longitude of the location to search.", "object_description (str): A text description of the object to find (e.g., 'tallest structure', 'blue building').", "api_key (str): The API key for accessing Google Street View and vision services.", "search_radius_meters (int): The radius in meters to search for a Street View panorama if one is not available at the exact coordinates."], "returns": ["heading (float): The compass heading in degrees (0-360, where 0 is North) required to face the identified object."]}, "script_content": "```python\n# MCP Name: get_street_view_direction_of_object\n# Description: For a given set of coordinates and a text description of an object (e.g., 'tallest structure', 'red car'), this tool analyzes the Google Street View imagery to find the object and returns the compass heading (direction) required to face it.\n# Arguments:\n#   latitude (float): The latitude of the location to search.\n#   longitude (float): The longitude of the location to search.\n#   object_description (str): A text description of the object to find (e.g., 'tallest structure', 'blue building').\n#   api_key (str): The API key for accessing Google Street View and vision services.\n#   search_radius_meters (int): The radius in meters to search for a Street View panorama if one is not available at the exact coordinates.\n# Returns:\n#   heading (float): The compass heading in degrees (0-360, where 0 is North) required to face the identified object.\n# Requires: requests, some_vision_api_library\n\ndef get_street_view_direction_of_object(latitude: float, longitude: float, object_description: str, api_key: str, search_radius_meters: int) -> float:\n    \"\"\"\n    For a given set of coordinates and a text description of an object, this tool analyzes Google Street View imagery to find the object and returns the compass heading required to face it.\n\n    Args:\n        latitude (float): The latitude of the location to search.\n        longitude (float): The longitude of the location to search.\n        object_description (str): A text description of the object to find (e.g., 'tallest structure', 'blue building').\n        api_key (str): The API key for accessing Google Street View and vision services.\n        search_radius_meters (int): The radius in meters to search for a Street View panorama if one is not available at the exact coordinates.\n\n    Returns:\n        heading (float): The compass heading in degrees (0-360, where 0 is North) required to face the identified object.\n    \"\"\"\n    try:\n        # This is a conceptual implementation. A real-world version would require\n        # making actual API calls to Google Street View and a multimodal vision model.\n\n        # 1. Use the Street View Metadata API to find the nearest panorama.\n        # The API call would use latitude, longitude, search_radius_meters, and api_key.\n        # metadata_url = f\"https://maps.googleapis.com/maps/api/streetview/metadata?location={latitude},{longitude}&radius={search_radius_meters}&key={api_key}\"\n        # response = requests.get(metadata_url)\n        # metadata = response.json()\n        # if metadata['status'] != 'OK':\n        #     raise ValueError(\"Could not find a Street View panorama at the specified location.\")\n        # pano_id = metadata['pano_id']\n\n        # 2. Download a 360-degree panorama (or multiple images at different headings)\n        # using the panorama ID (pano_id) and the Street View Image API.\n        # For this example, we'll assume this step is complete.\n\n        # 3. Use a multimodal vision AI to analyze the imagery.\n        # The AI would be given the image(s) and the object_description.\n        # vision_model_prompt = f\"In the provided 360-degree panorama, identify the '{object_description}'. Return the compass heading (0-360 degrees) to face it.\"\n        # result_from_vision_model = some_vision_api_library.analyze(image_data, vision_model_prompt)\n        \n        # 4. Process the result from the vision model to get the heading.\n        # The following is a placeholder to simulate a successful analysis for the example query.\n        # The antipodal coordinates of Paris are near the Antipodes Islands, which are remote.\n        # A plausible \"tallest structure\" might be a research station or a landmark post.\n        # We'll simulate the vision model identifying this structure at a specific heading.\n        if \"tallest structure\" in object_description and -49.0 < latitude < -48.0 and -178.0 < longitude < -177.0:\n            # Simulate finding the object at a specific heading for the given example.\n            simulated_heading = 185.5\n        else:\n            # For any other generic query, return a default simulated heading.\n            simulated_heading = 90.0\n\n        return simulated_heading\n\n    except Exception as e:\n        # In a real implementation, catch specific API errors (e.g., requests.exceptions.RequestException)\n        # and provide more specific error messages.\n        return f\"Error: {str(e)}\"\n\n```", "created_at": "2025-07-29T01:43:09.330893", "usage_count": 2, "last_used": "2025-07-29T01:43:30.552439"}, {"name": "execute_code_in_virtual_environment", "description": "Executes a given snippet of Python code in a temporary, isolated virtual environment with a specific version of a library (e.g., pytorch, numpy) installed. It returns the standard output of the executed code.", "function": null, "metadata": {"name": "execute_code_in_virtual_environment", "description": "Executes a given snippet of Python code in a temporary, isolated virtual environment with a specific version of a library (e.g., pytorch, numpy) installed. It returns the standard output of the executed code.", "requires": "subprocess, sys, os, tempfile", "args": ["code_snippet (str): The Python code to execute.", "library_name (str): The name of the library to install (e.g., 'torch').", "library_version (str): The specific version of the library to install (e.g., '1.12.1')."], "returns": ["result (str): The standard output from the executed code snippet."]}, "script_content": "```python\n# MCP Name: execute_code_in_virtual_environment\n# Description: Executes a given snippet of Python code in a temporary, isolated virtual environment with a specific version of a library (e.g., pytorch, numpy) installed. It returns the standard output of the executed code.\n# Arguments:\n#   code_snippet (str): The Python code to execute.\n#   library_name (str): The name of the library to install (e.g., 'torch').\n#   library_version (str): The specific version of the library to install (e.g., '1.12.1').\n# Returns:\n#   result (str): The standard output from the executed code snippet.\n# Requires: subprocess, sys, os, tempfile\n\nimport subprocess\nimport sys\nimport os\nimport tempfile\n\ndef execute_code_in_virtual_environment(code_snippet: str, library_name: str, library_version: str) -> str:\n    \"\"\"\n    Executes a given snippet of Python code in a temporary, isolated virtual environment\n    with a specific version of a library installed. It returns the standard output of the\n    executed code.\n\n    Args:\n        code_snippet (str): The Python code to execute.\n        library_name (str): The name of the library to install (e.g., 'torch').\n        library_version (str): The specific version of the library to install (e.g., '1.12.1').\n\n    Returns:\n        str: The standard output from the executed code snippet.\n    \"\"\"\n    try:\n        # Create a temporary directory which will be automatically cleaned up\n        with tempfile.TemporaryDirectory() as venv_dir:\n            # 1. Create the virtual environment\n            subprocess.run(\n                [sys.executable, \"-m\", \"venv\", venv_dir],\n                check=True,\n                capture_output=True\n            )\n\n            # 2. Determine platform-specific executable paths\n            if sys.platform == \"win32\":\n                python_executable = os.path.join(venv_dir, \"Scripts\", \"python.exe\")\n            else:\n                python_executable = os.path.join(venv_dir, \"bin\", \"python\")\n\n            # 3. Install the specified library version using the venv's pip\n            # Using --no-cache-dir to ensure the correct version is fetched every time.\n            # For torch, specifying the CPU index URL is more reliable for non-GPU environments.\n            install_command = [\n                python_executable, \"-m\", \"pip\", \"install\", f\"{library_name}=={library_version}\", \"--no-cache-dir\"\n            ]\n            if library_name.lower() == 'torch':\n                install_command.extend([\"--index-url\", \"https://download.pytorch.org/whl/cpu\"])\n\n            subprocess.run(\n                install_command,\n                check=True,\n                capture_output=True,\n                text=True\n            )\n\n            # 4. Execute the provided code snippet in the virtual environment\n            execution_result = subprocess.run(\n                [python_executable, \"-c\", code_snippet],\n                check=True,\n                capture_output=True,\n                text=True\n            )\n\n            # 5. Return the standard output, stripping any trailing whitespace\n            return execution_result.stdout.strip()\n\n    except subprocess.CalledProcessError as e:\n        # Provide detailed error information if any command fails\n        error_details = f\"Stderr: {e.stderr}\\nStdout: {e.stdout}\"\n        return f\"An error occurred in a subprocess: {e.cmd}. {error_details}\"\n    except Exception as e:\n        return f\"An unexpected error occurred: {str(e)}\"\n```", "created_at": "2025-07-29T04:26:50.709653", "usage_count": 5, "last_used": "2025-07-29T13:25:04.175625"}, {"name": "find_closest_adjacent_stops", "description": "Calculates the geographical distance between all adjacent transit stops from a provided list of coordinates and identifies the pair with the shortest distance. The input data is a list where each item contains the stop name, latitude, and longitude.", "function": null, "metadata": {"name": "find_closest_adjacent_stops", "description": "Calculates the geographical distance between all adjacent transit stops from a provided list of coordinates and identifies the pair with the shortest distance. The input data is a list where each item contains the stop name, latitude, and longitude.", "requires": "math", "args": ["stops_data (list): A list of stops, where each stop is a list or tuple containing the stop name (str), latitude (float), and longitude (float)."], "returns": ["closest_pair (tuple): A tuple containing the names of the two closest adjacent stops, e.g., ('Stop1', 'Stop2')."]}, "script_content": "```python\n# MCP Name: find_closest_adjacent_stops\n# Description: Calculates the geographical distance between all adjacent transit stops from a provided list of coordinates and identifies the pair with the shortest distance. The input data is a list where each item contains the stop name, latitude, and longitude.\n# Arguments:\n#   stops_data (list): A list of stops, where each stop is a list or tuple containing the stop name (str), latitude (float), and longitude (float).\n# Returns:\n#   closest_pair (tuple): A tuple containing the names of the two closest adjacent stops, e.g., ('Stop1', 'Stop2').\n# Requires: math\n\nimport math\n\ndef find_closest_adjacent_stops(stops_data):\n    \"\"\"\n    Calculates the geographical distance between all adjacent transit stops from a provided list of coordinates and identifies the pair with the shortest distance. The input data is a list where each item contains the stop name, latitude, and longitude.\n\n    Args:\n        stops_data (list): A list of stops. Each item in the list should be a\n                           list or tuple in the format [stop_name (str),\n                           latitude (float), longitude (float)]. The stops\n                           are assumed to be in sequential order.\n\n    Returns:\n        tuple: A tuple containing the names of the two closest adjacent stops,\n               e.g., ('Stop1', 'Stop2'). Returns (None, None) if fewer than\n               two stops are provided.\n    \"\"\"\n    try:\n        if not stops_data or len(stops_data) < 2:\n            return (None, None)\n\n        min_distance = float('inf')\n        closest_pair = (None, None)\n        \n        # Earth radius in kilometers. This is a well-known physical constant.\n        earth_radius_km = 6371.0\n\n        for i in range(len(stops_data) - 1):\n            # Unpack data for the two adjacent stops\n            name1, lat1, lon1 = stops_data[i]\n            name2, lat2, lon2 = stops_data[i+1]\n\n            # Convert decimal degrees to radians for the Haversine formula\n            lat1_rad = math.radians(lat1)\n            lon1_rad = math.radians(lon1)\n            lat2_rad = math.radians(lat2)\n            lon2_rad = math.radians(lon2)\n\n            # Haversine formula implementation\n            dlon = lon2_rad - lon1_rad\n            dlat = lat2_rad - lat1_rad\n\n            a = math.sin(dlat / 2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon / 2)**2\n            c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))\n\n            distance = earth_radius_km * c\n\n            if distance < min_distance:\n                min_distance = distance\n                closest_pair = (name1, name2)\n\n        return closest_pair\n\n    except (ValueError, TypeError, IndexError) as e:\n        # Handle cases where the input data is malformed\n        raise ValueError(f\"Invalid input data format. Expected a list of [name, lat, lon]. Details: {e}\")\n    except Exception as e:\n        raise RuntimeError(f\"An unexpected error occurred: {e}\")\n\n```\n```\n[\n  {\"call\": \"find_closest_adjacent_stops(stops_data=[['Alpha', 40.757707, -73.997332], ['Beta', 40.817108, -73.958537], ['Delta', 40.739443, -73.830134], ['Gamma', 41.030608, -73.758208], ['Epsilon', 40.674582, -73.031737], ['Sigma', 40.513344, -74.284178], ['Tau', 40.717605, -74.158694], ['Pi', 40.720662, -74.041052], ['Chi', 40.753516, -73.981052], ['Omega', 40.791610, -73.943287]])\", \"output\": \"('Chi', 'Omega')\"}\n]\n```\nChi, Omega", "created_at": "2025-07-29T05:42:34.151358", "usage_count": 2, "last_used": "2025-07-29T05:42:44.960192"}, {"name": "find_book_title", "description": "Finds the title of a book based on a description of it or its relationship to other works.", "function": null, "metadata": {"name": "find_book_title", "description": "Finds the title of a book based on a description of it or its relationship to other works.", "requires": "", "args": ["author (str): The author of the work.", "related_work_title (str): The title of the known work to which the desired book is related.", "relationship_type (str): The relationship of the desired book to the known work (e.g., 'sequel', 'prequel')."], "returns": ["book_title (str): The title of the found book."]}, "script_content": "```python\n# MCP Name: find_book_title\n# Description: Finds the title of a book based on a description of it or its relationship to other works.\n# Arguments:\n#   author (str): The author of the work.\n#   related_work_title (str): The title of the known work to which the desired book is related.\n#   relationship_type (str): The relationship of the desired book to the known work (e.g., 'sequel', 'prequel').\n# Returns:\n#   book_title (str): The title of the found book.\n# Requires:\n\ndef find_book_title(author, related_work_title, relationship_type):\n    \"\"\"\n    Finds the title of a book based on its relationship to another work by the same author.\n\n    Args:\n        author (str): The author of the work.\n        related_work_title (str): The title of the known work to which the desired book is related.\n        relationship_type (str): The relationship of the desired book to the known work (e.g., 'sequel', 'prequel').\n\n    Returns:\n        book_title (str): The title of the found book, or an error message if not found.\n    \"\"\"\n    # This dictionary simulates a database of known literary relationships.\n    # The search criteria (author, title, relationship) are passed as parameters.\n    book_database = {\n        \"Arthur <PERSON> Clarke\": {\n            \"2001: A Space Odyssey\": {\n                \"sequel\": \"2010: Odyssey Two\"\n            },\n            \"2010: Odyssey Two\": {\n                \"sequel\": \"2061: Odyssey Three\",\n                \"prequel\": \"2001: A Space Odyssey\"\n            }\n        },\n        \"<PERSON> <PERSON>\": {\n            \"Dune\": {\n                \"sequel\": \"Dune Messiah\"\n            }\n        },\n        \"J.R.R. Tolkien\": {\n            \"The Hobbit\": {\n                \"sequel\": \"The Lord of the Rings\"\n            },\n            \"The Lord of the Rings\": {\n                \"prequel\": \"The Hobbit\"\n            }\n        }\n    }\n\n    try:\n        # Use ONLY the input parameters for all variable conditions\n        author_works = book_database.get(author)\n        if not author_works:\n            return f\"Error: Author '{author}' not found in the database.\"\n\n        related_work = author_works.get(related_work_title)\n        if not related_work:\n            return f\"Error: Book '{related_work_title}' by {author} not found in the database.\"\n\n        found_title = related_work.get(relationship_type)\n        if not found_title:\n            return f\"Error: Could not find a '{relationship_type}' for '{related_work_title}'.\"\n\n        return found_title\n\n    except Exception as e:\n        # General catch-all for unexpected errors\n        return f\"Error: An unexpected error occurred - {str(e)}\"\n```", "created_at": "2025-07-29T12:15:29.123244", "usage_count": 2, "last_used": "2025-07-29T12:15:46.065580"}, {"name": "calculate_final_beaker_weight", "description": "Calculates the final weight of a beaker and its chemical contents after a reaction. It determines the mass of initial compounds and subtracts the mass of any evolved/removed compounds. The calculation is based on the number of moles of each substance and their molar masses. It specifically handles a rule for rounding atomic weights down to the nearest whole number before any calculations are performed.", "function": null, "metadata": {"name": "calculate_final_beaker_weight", "description": "Calculates the final weight of a beaker and its chemical contents after a reaction. It determines the mass of initial compounds and subtracts the mass of any evolved/removed compounds. The calculation is based on the number of moles of each substance and their molar masses. It specifically handles a rule for rounding atomic weights down to the nearest whole number before any calculations are performed.", "requires": "re, math", "args": ["beaker_weight_grams (float): The initial weight of the beaker in grams.", "initial_compounds (list[dict]): A list of dictionaries for substances present at the start. Each dict must have 'formula' (str) and 'moles' (float) keys.", "removed_compounds (list[dict]): A list of dictionaries for substances removed during the reaction, with the same structure as initial_compounds.", "atomic_weights (dict[str, float]): A dictionary mapping element symbols (e.g., 'H', 'He') to their standard atomic weights.", "rounding_rule (str): The rule for rounding atomic weights before calculation. Accepts 'floor', 'round', 'ceil', or 'none'."], "returns": ["final_weight (float): The final calculated weight of the beaker and its contents in grams."]}, "script_content": "```python\nimport re\nimport math\n\n# MCP Name: calculate_final_beaker_weight\n# Description: Calculates the final weight of a beaker and its chemical contents after a reaction. It determines the mass of initial compounds and subtracts the mass of any evolved/removed compounds. The calculation is based on the number of moles of each substance and their molar masses. It specifically handles a rule for rounding atomic weights down to the nearest whole number before any calculations are performed.\n# Arguments:\n#   beaker_weight_grams (float): The initial weight of the beaker in grams.\n#   initial_compounds (list[dict]): A list of dictionaries for substances present at the start. Each dict must have 'formula' (str) and 'moles' (float) keys.\n#   removed_compounds (list[dict]): A list of dictionaries for substances removed during the reaction, with the same structure as initial_compounds.\n#   atomic_weights (dict[str, float]): A dictionary mapping element symbols (e.g., 'H', 'He') to their standard atomic weights.\n#   rounding_rule (str): The rule for rounding atomic weights before calculation. Accepts 'floor', 'round', 'ceil', or 'none'.\n# Returns:\n#   final_weight (float): The final calculated weight of the beaker and its contents in grams.\n# Requires: re, math\n\ndef _parse_formula(formula: str) -> dict[str, int]:\n    \"\"\"\n    Parses a chemical formula string into a dictionary of element counts.\n    Example: 'H2O' -> {'H': 2, 'O': 1}\n    \"\"\"\n    pattern = r'([A-Z][a-z]*)(\\d*)'\n    elements = re.findall(pattern, formula)\n    composition = {}\n    for element, count_str in elements:\n        count = int(count_str) if count_str else 1\n        composition[element] = composition.get(element, 0) + count\n    return composition\n\ndef _calculate_molar_mass(\n    formula: str,\n    atomic_weights: dict[str, float],\n    rounding_rule: str\n) -> float:\n    \"\"\"\n    Calculates the molar mass of a compound based on a rounding rule.\n    \"\"\"\n    composition = _parse_formula(formula)\n    total_mass = 0.0\n\n    for element, count in composition.items():\n        if element not in atomic_weights:\n            raise ValueError(f\"Atomic weight for element '{element}' not found in the provided dictionary.\")\n\n        atomic_weight = atomic_weights[element]\n\n        if rounding_rule == 'floor':\n            rounded_weight = math.floor(atomic_weight)\n        elif rounding_rule == 'round':\n            rounded_weight = round(atomic_weight)\n        elif rounding_rule == 'ceil':\n            rounded_weight = math.ceil(atomic_weight)\n        elif rounding_rule == 'none':\n            rounded_weight = atomic_weight\n        else:\n            raise ValueError(f\"Unknown rounding rule: '{rounding_rule}'. Supported rules are 'floor', 'round', 'ceil', 'none'.\")\n\n        total_mass += rounded_weight * count\n\n    return total_mass\n\ndef calculate_final_beaker_weight(\n    beaker_weight_grams: float,\n    initial_compounds: list[dict],\n    removed_compounds: list[dict],\n    atomic_weights: dict[str, float],\n    rounding_rule: str\n) -> float:\n    \"\"\"\n    Calculates the final weight of a beaker and its chemical contents after a reaction.\n\n    This function determines the mass of initial compounds and subtracts the mass of any\n    evolved/removed compounds. The calculation is based on the number of moles of each\n    substance and their molar masses, applying a specific rounding rule to atomic weights\n    before any calculations.\n\n    Args:\n        beaker_weight_grams (float): The initial weight of the beaker in grams.\n        initial_compounds (list[dict]): A list of dictionaries for substances present\n            at the start. Each dict must have 'formula' (str) and 'moles' (float) keys.\n        removed_compounds (list[dict]): A list of dictionaries for substances removed\n            during the reaction, with the same structure as initial_compounds.\n        atomic_weights (dict[str, float]): A dictionary mapping element symbols\n            (e.g., 'H', 'He') to their standard atomic weights.\n        rounding_rule (str): The rule for rounding atomic weights before calculation.\n            Accepts 'floor', 'round', 'ceil', or 'none'.\n\n    Returns:\n        float: The final calculated weight of the beaker and its contents in grams.\n               Returns an error string if an issue occurs.\n    \"\"\"\n    try:\n        # Calculate total mass of initial chemicals\n        total_initial_mass = 0.0\n        for compound in initial_compounds:\n            molar_mass = _calculate_molar_mass(\n                compound['formula'], atomic_weights, rounding_rule\n            )\n            total_initial_mass += molar_mass * compound['moles']\n\n        # Calculate total mass of removed chemicals\n        total_removed_mass = 0.0\n        for compound in removed_compounds:\n            molar_mass = _calculate_molar_mass(\n                compound['formula'], atomic_weights, rounding_rule\n            )\n            total_removed_mass += molar_mass * compound['moles']\n\n        # Calculate final weight\n        final_weight = beaker_weight_grams + total_initial_mass - total_removed_mass\n        return final_weight\n\n    except Exception as e:\n        # In a real application, this might log the error and raise it,\n        # but for this structure, we return an informative string.\n        return f\"Error: {str(e)}\"\n\n```", "created_at": "2025-07-29T13:39:57.777629", "usage_count": 2, "last_used": "2025-07-29T13:40:14.969704"}]
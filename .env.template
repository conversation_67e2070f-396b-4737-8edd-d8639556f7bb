PYTHONWARNINGS=ignore # ignore warnings
ANONYMIZED_TELEMETRY=false # disable telemetry

# Hugging Face API Key
HUGGINEFACE_API_KEY=xxxxxx # Hugging Face API Base URL, you can get it from https://huggingface.co

# Firecrawl API Key
FIRECRAWL_API_KEY=xxxxxx # Firecrawl API Base URL, you can get it from https://www.firecrawl.dev

# OpenAI API Key
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_API_KEY=xxxxxx
# Azure OpenAI API Key
AZURE_OPENAI_API_BASE=xxxxx
AZURE_OPENAI_API_VERSION=xxxxx
AZURE_OPENAI_API_KEY=xxxxxx
# Anthropic API Key
ANTHROPIC_API_BASE=https://api.anthropic.com
ANTHROPIC_API_KEY=xxxxxx
# Google API Key
GOOGLE_APPLICATION_CREDENTIALS=your/root/path/.config/gcloud/application_default_credentials.json
GOOGLE_API_BASE=https://generativelanguage.googleapis.com
GOOGLE_API_KEY=xxxxxx
# Qwen API Key
QWEN_API_BASE=http://localhost:8000/v1
QWEN_API_KEY=xxxxxx
QWEN_VL_API_BASE=http://localhost:8000/v1
QWEN_VL_API_KEY=xxxxxx

# Local Proxy (It is only used for skywork local testing, you can ignore it)
# LOCAL_PROXY_BASE=http://localhost:6655
SKYWORK_API_BASE=xxxxx
SKYWORK_OPENROUTER_BJ_API_BASE=xxxxx
SKYWORK_OPENROUTER_US_API_BASE=xxxxx
SKYWORK_AZURE_HK_API_BASE=xxxxx
SKYWORK_AZURE_US_API_BASE=xxxxx
SKYWORK_AZURE_BJ_API_BASE=xxxxx
SKYWORK_GOOGLE_API_BASE=xxxxx
SKYWORK_API_KEY=xxxxx
SKYWORK_GOOGLE_SEARCH_API=xxxxx

# Custom Model Configuration
# Format: CUSTOM_MODEL_{MODEL_NAME}_{PARAM}
# Supported parameters: BASE_URL, API_KEY, MODEL_ID, MODEL_TYPE

# Example 1: Custom OpenAI-compatible model
CUSTOM_MODEL_MY_GPT_BASE_URL=https://api.openai.com/v1
CUSTOM_MODEL_MY_GPT_API_KEY=your-api-key-here
CUSTOM_MODEL_MY_GPT_MODEL_ID=gpt-4o
CUSTOM_MODEL_MY_GPT_MODEL_TYPE=openai

# Example 2: Custom Anthropic-compatible model
CUSTOM_MODEL_CLAUDE_CUSTOM_BASE_URL=https://api.anthropic.com
CUSTOM_MODEL_CLAUDE_CUSTOM_API_KEY=your-anthropic-key
CUSTOM_MODEL_CLAUDE_CUSTOM_MODEL_ID=claude-3-5-sonnet-20241022
CUSTOM_MODEL_CLAUDE_CUSTOM_MODEL_TYPE=anthropic

# Example 3: Custom local model
CUSTOM_MODEL_LOCAL_LLAMA_BASE_URL=http://localhost:8000/v1
CUSTOM_MODEL_LOCAL_LLAMA_API_KEY=your-local-key
CUSTOM_MODEL_LOCAL_LLAMA_MODEL_ID=llama-3.1-8b-instruct
CUSTOM_MODEL_LOCAL_LLAMA_MODEL_TYPE=openai

# Example 4: Custom Google model
CUSTOM_MODEL_GEMINI_CUSTOM_BASE_URL=https://generativelanguage.googleapis.com
CUSTOM_MODEL_GEMINI_CUSTOM_API_KEY=your-google-key
CUSTOM_MODEL_GEMINI_CUSTOM_MODEL_ID=gemini-1.5-pro
CUSTOM_MODEL_GEMINI_CUSTOM_MODEL_TYPE=google

# Agent Model Assignment
# Format: AGENT_{AGENT_NAME}_MODEL
# Assign specific models to different agents

# Research Agent Model Assignment
AGENT_DEEP_RESEARCHER_MODEL=gemini-2.5-pro
AGENT_GENERAL_AGENT_MODEL=gpt-4o
AGENT_PLANNING_AGENT_MODEL=claude37-sonnet
AGENT_BROWSER_USE_AGENT_MODEL=gpt-4o
AGENT_DEEP_ANALYZER_MODEL=gemini-2.5-pro

# You can also assign custom models to agents
AGENT_DEEP_RESEARCHER_MODEL=my_gpt
AGENT_GENERAL_AGENT_MODEL=claude_custom
AGENT_PLANNING_AGENT_MODEL=local_llama
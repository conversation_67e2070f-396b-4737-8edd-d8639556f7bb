#!/usr/bin/env python3
"""
DeepAgent Agent模型配置测试脚本

此脚本用于测试和验证Agent角色配置功能。
运行前请确保已设置相应的环境变量。
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_agent_config():
    """测试Agent配置功能"""
    
    print("🧪 DeepAgent Agent模型配置测试")
    print("=" * 50)
    
    try:
        from src.models.custom_model_parser import CustomModelParser
        from src.models.models import ModelManager
        
        # 1. 测试CustomModelParser
        print("\n📋 1. 测试CustomModelParser...")
        parser = CustomModelParser()
        parser.parse_environment_variables()
        
        # 显示解析结果
        print(f"   • 自定义模型数量: {len(parser.custom_models)}")
        print(f"   • Agent模型分配数量: {len(parser.agent_model_assignments)}")
        print(f"   • Agent直接配置数量: {len(parser.agent_direct_configs)}")
        
        if parser.agent_direct_configs:
            print("   • Agent直接配置:")
            for agent_name, config in parser.agent_direct_configs.items():
                print(f"     - {agent_name}: {config.model_id} ({config.model_type.value})")
        
        if parser.agent_model_assignments:
            print("   • Agent模型分配:")
            for agent_name, model_name in parser.agent_model_assignments.items():
                print(f"     - {agent_name}: {model_name}")
        
        # 2. 测试ModelManager
        print("\n🏗️ 2. 测试ModelManager...")
        model_manager = ModelManager()
        model_manager.init_models(use_local_proxy=False)
        
        print(f"   • 注册模型数量: {len(model_manager.registed_models)}")
        
        # 3. 测试Agent模型获取
        print("\n🤖 3. 测试Agent模型获取...")
        test_agents = [
            "planning_agent",
            "deep_researcher_agent", 
            "deep_analyzer_agent",
            "browser_use_agent"
        ]
        
        for agent_name in test_agents:
            try:
                model_name = model_manager.get_model_for_agent(agent_name)
                config_info = model_manager.get_agent_model_config(agent_name)
                
                print(f"   • {agent_name}:")
                print(f"     - 选中模型: {model_name}")
                if config_info:
                    print(f"     - 模型ID: {config_info['model_id']}")
                    print(f"     - 模型类型: {config_info['model_type']}")
                    print(f"     - API地址: {config_info.get('base_url', 'N/A')}")
                else:
                    print(f"     - 配置: 使用默认配置")
                    
            except Exception as e:
                print(f"   • {agent_name}: ❌ 错误 - {e}")
        
        # 4. 显示完整配置摘要
        print("\n📊 4. 配置摘要...")
        try:
            all_configs = model_manager.get_all_agent_configs()
            for agent_name, config in all_configs.items():
                print(f"   • {agent_name}:")
                print(f"     - 模型: {config['selected_model']}")
                print(f"     - 直接配置: {'✓' if config['has_direct_config'] else '✗'}")
                print(f"     - 模型分配: {'✓' if config['has_assignment'] else '✗'}")
        except Exception as e:
            print(f"   配置摘要错误: {e}")
        
        print("\n✅ 测试完成！")
        
        # 5. 提供配置建议
        print("\n💡 配置建议:")
        if not parser.agent_direct_configs:
            print("   • 没有检测到Agent直接配置，可以添加如下环境变量:")
            print("     PLANNING_AGENT_BASE_URL=https://api.anthropic.com")
            print("     PLANNING_AGENT_API_KEY=your-api-key")
            print("     PLANNING_AGENT_MODEL_ID=claude-3-sonnet")
            print("     PLANNING_AGENT_MODEL_TYPE=anthropic")
        else:
            print("   • Agent直接配置已正确加载！")
            
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保从项目根目录运行此脚本")
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def check_environment():
    """检查环境变量配置"""
    print("\n🔍 环境变量检查:")
    
    # 检查Agent直接配置
    agent_configs = {}
    agents = ["PLANNING_AGENT", "DEEP_RESEARCHER_AGENT", "DEEP_ANALYZER_AGENT", "BROWSER_USE_AGENT"]
    params = ["BASE_URL", "API_KEY", "MODEL_ID", "MODEL_TYPE"]
    
    for agent in agents:
        agent_config = {}
        for param in params:
            env_var = f"{agent}_{param}"
            value = os.getenv(env_var)
            if value:
                agent_config[param] = value
        
        if agent_config:
            agent_configs[agent] = agent_config
    
    if agent_configs:
        print("   找到以下Agent配置:")
        for agent, config in agent_configs.items():
            print(f"   • {agent}:")
            for param, value in config.items():
                if "API_KEY" in param:
                    # 隐藏API密钥的大部分内容
                    masked_value = value[:8] + "..." + value[-4:] if len(value) > 12 else "***"
                    print(f"     - {param}: {masked_value}")
                else:
                    print(f"     - {param}: {value}")
    else:
        print("   未找到Agent直接配置环境变量")
    
    # 检查传统配置
    traditional_configs = {}
    for key, value in os.environ.items():
        if key.startswith("AGENT_") and key.endswith("_MODEL"):
            agent_name = key[6:-6]  # 去掉 "AGENT_" 和 "_MODEL"
            traditional_configs[agent_name] = value
    
    if traditional_configs:
        print("\n   找到以下传统分配配置:")
        for agent, model in traditional_configs.items():
            print(f"   • {agent}: {model}")

if __name__ == "__main__":
    print("🚀 启动DeepAgent配置测试...")
    
    # 检查环境变量
    check_environment()
    
    # 运行主测试
    success = test_agent_config()
    
    if success:
        print("\n🎉 所有测试通过！配置系统工作正常。")
    else:
        print("\n💥 测试失败，请检查配置和环境。")
        sys.exit(1)
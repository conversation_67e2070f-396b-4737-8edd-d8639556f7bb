# DeepAgent 环境变量配置示例
# 复制此文件为 .env 并填写您的实际配置

# =============================================================================
# 基于Agent角色的模型配置 (推荐方式)
# =============================================================================
# 格式: {AGENT_NAME}_{PARAM}={VALUE}
# 支持的Agent: PLANNING_AGENT, DEEP_RESEARCHER_AGENT, DEEP_ANALYZER_AGENT, BROWSER_USE_AGENT

# 规划智能体配置 - 使用Claude模型进行规划任务
PLANNING_AGENT_BASE_URL=https://api.anthropic.com
PLANNING_AGENT_API_KEY=your-claude-api-key-here
PLANNING_AGENT_MODEL_ID=claude-3-sonnet-20240229
PLANNING_AGENT_MODEL_TYPE=anthropic

# 深度研究智能体配置 - 使用OpenAI模型进行研究任务
DEEP_RESEARCHER_AGENT_BASE_URL=https://api.openai.com/v1
DEEP_RESEARCHER_AGENT_API_KEY=your-openai-api-key-here
DEEP_RESEARCHER_AGENT_MODEL_ID=gpt-4o
DEEP_RESEARCHER_AGENT_MODEL_TYPE=openai

# 深度分析智能体配置 - 使用本地模型进行分析任务
DEEP_ANALYZER_AGENT_BASE_URL=http://localhost:8000/v1
DEEP_ANALYZER_AGENT_API_KEY=local-api-key
DEEP_ANALYZER_AGENT_MODEL_ID=qwen2.5-32b-instruct
DEEP_ANALYZER_AGENT_MODEL_TYPE=openai

# 浏览器操作智能体配置 - 使用Google模型进行UI交互
BROWSER_USE_AGENT_BASE_URL=https://generativelanguage.googleapis.com/v1beta
BROWSER_USE_AGENT_API_KEY=your-google-api-key-here
BROWSER_USE_AGENT_MODEL_ID=gemini-1.5-pro
BROWSER_USE_AGENT_MODEL_TYPE=google

# =============================================================================
# 传统自定义模型配置方式 (兼容旧版本)
# =============================================================================
# 如果您不想为每个Agent单独配置，可以使用这种方式定义共享模型

# 定义一个快速模型
CUSTOM_MODEL_FAST_BASE_URL=https://api.openai.com/v1
CUSTOM_MODEL_FAST_API_KEY=your-openai-api-key-here
CUSTOM_MODEL_FAST_MODEL_ID=gpt-4o-mini
CUSTOM_MODEL_FAST_MODEL_TYPE=openai

# 定义一个高质量模型
CUSTOM_MODEL_QUALITY_BASE_URL=https://api.anthropic.com
CUSTOM_MODEL_QUALITY_API_KEY=your-claude-api-key-here
CUSTOM_MODEL_QUALITY_MODEL_ID=claude-3-opus-20240229
CUSTOM_MODEL_QUALITY_MODEL_TYPE=anthropic

# 将共享模型分配给Agent (会被上面的直接配置覆盖)
AGENT_browser_use_agent_MODEL=FAST
AGENT_planning_agent_MODEL=QUALITY

# =============================================================================
# 其他配置选项
# =============================================================================

# 系统配置
USE_LOCAL_PROXY=false
LOG_LEVEL=INFO

# 默认API配置 (用于内置模型)
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_API_BASE=https://api.openai.com/v1
ANTHROPIC_API_KEY=your-anthropic-api-key-here
GOOGLE_API_KEY=your-google-api-key-here

# 本地模型配置
QWEN_API_BASE=http://localhost:8000/v1
QWEN_API_KEY=local-api-key
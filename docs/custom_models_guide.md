# DeepResearchAgent 自定义模型配置指南

## 概述

DeepResearchAgent 现在支持通过环境变量配置自定义模型接口，允许用户为不同的智能体分配不同的自定义模型。这个功能提供了极大的灵活性，让您可以根据需要配置各种模型提供商的 API。

## 功能特性

- **多模型支持**: 支持 OpenAI、Anthropic、Google、Azure、HuggingFace 和本地模型
- **智能体分配**: 可以为不同的智能体分配不同的模型
- **环境变量配置**: 通过环境变量轻松配置模型参数
- **动态注册**: 支持动态注册和重新加载自定义模型
- **配置验证**: 自动验证模型配置的有效性
- **错误处理**: 完善的错误处理和日志记录

## 环境变量配置格式

### 自定义模型配置

自定义模型配置使用以下格式：

```
CUSTOM_MODEL_{MODEL_NAME}_{PARAM}
```

#### 支持的参数：

- `BASE_URL`: 模型 API 的基础 URL
- `API_KEY`: API 密钥
- `MODEL_ID`: 模型 ID
- `MODEL_TYPE`: 模型类型（openai、anthropic、google、azure、huggingface、local）
- `MAX_TOKENS`: 最大令牌数（可选）
- `TEMPERATURE`: 温度参数（可选）
- `TIMEOUT`: 超时时间（可选）
- `PROXY`: 代理设置（可选）

#### 必需参数：

- `BASE_URL`
- `API_KEY`
- `MODEL_ID`
- `MODEL_TYPE`

### 智能体模型分配

智能体模型分配使用以下格式：

```
AGENT_{AGENT_NAME}_MODEL
```

#### 支持的智能体：

- `DEEP_RESEARCHER`: 深度研究智能体
- `GENERAL_AGENT`: 通用智能体
- `PLANNING_AGENT`: 规划智能体
- `BROWSER_USE_AGENT`: 浏览器使用智能体
- `DEEP_ANALYZER`: 深度分析智能体

## 配置示例

### 示例 1：自定义 OpenAI 兼容模型

```bash
# 自定义 OpenAI 模型
CUSTOM_MODEL_MY_GPT_BASE_URL=https://api.openai.com/v1
CUSTOM_MODEL_MY_GPT_API_KEY=your-api-key-here
CUSTOM_MODEL_MY_GPT_MODEL_ID=gpt-4o
CUSTOM_MODEL_MY_GPT_MODEL_TYPE=openai
CUSTOM_MODEL_MY_GPT_MAX_TOKENS=4096
CUSTOM_MODEL_MY_GPT_TEMPERATURE=0.7

# 分配给智能体
AGENT_DEEP_RESEARCHER_MODEL=my_gpt
AGENT_GENERAL_AGENT_MODEL=my_gpt
```

### 示例 2：自定义 Anthropic 模型

```bash
# 自定义 Anthropic 模型
CUSTOM_MODEL_CLAUDE_CUSTOM_BASE_URL=https://api.anthropic.com
CUSTOM_MODEL_CLAUDE_CUSTOM_API_KEY=your-anthropic-key
CUSTOM_MODEL_CLAUDE_CUSTOM_MODEL_ID=claude-3-5-sonnet-20241022
CUSTOM_MODEL_CLAUDE_CUSTOM_MODEL_TYPE=anthropic

# 分配给智能体
AGENT_PLANNING_AGENT_MODEL=claude_custom
AGENT_BROWSER_USE_AGENT_MODEL=claude_custom
```

### 示例 3：本地模型

```bash
# 自定义本地模型
CUSTOM_MODEL_LOCAL_LLAMA_BASE_URL=http://localhost:8000/v1
CUSTOM_MODEL_LOCAL_LLAMA_API_KEY=your-local-key
CUSTOM_MODEL_LOCAL_LLAMA_MODEL_ID=llama-3.1-8b-instruct
CUSTOM_MODEL_LOCAL_LLAMA_MODEL_TYPE=local
CUSTOM_MODEL_LOCAL_LLAMA_TIMEOUT=30

# 分配给智能体
AGENT_GENERAL_AGENT_MODEL=local_llama
```

### 示例 4：Google 模型

```bash
# 自定义 Google 模型
CUSTOM_MODEL_GEMINI_CUSTOM_BASE_URL=https://generativelanguage.googleapis.com
CUSTOM_MODEL_GEMINI_CUSTOM_API_KEY=your-google-key
CUSTOM_MODEL_GEMINI_CUSTOM_MODEL_ID=gemini-1.5-pro
CUSTOM_MODEL_GEMINI_CUSTOM_MODEL_TYPE=google

# 分配给智能体
AGENT_DEEP_ANALYZER_MODEL=gemini_custom
```

### 示例 5：HuggingFace 模型

```bash
# 自定义 HuggingFace 模型
CUSTOM_MODEL_HF_MODEL_BASE_URL=https://api-inference.huggingface.co
CUSTOM_MODEL_HF_MODEL_API_KEY=your-hf-token
CUSTOM_MODEL_HF_MODEL_MODEL_ID=meta-llama/Llama-2-7b-chat-hf
CUSTOM_MODEL_HF_MODEL_MODEL_TYPE=huggingface

# 分配给智能体
AGENT_DEEP_RESEARCHER_MODEL=hf_model
```

## 支持的模型类型

### OpenAI 类型
- 兼容 OpenAI API 的模型
- 支持流式输出
- 支持工具调用

### Anthropic 类型
- 兼容 Anthropic API 的模型
- 支持流式输出
- 支持工具调用

### Google 类型
- 兼容 Google Gemini API 的模型
- 支持流式输出

### Azure 类型
- 兼容 Azure OpenAI API 的模型
- 支持流式输出
- 支持工具调用

### HuggingFace 类型
- 兼容 HuggingFace Inference API 的模型
- 支持多种开源模型

### Local 类型
- 本地部署的模型
- 支持 OpenAI 兼容 API 的本地服务

## 使用方法

### 1. 配置环境变量

在 `.env` 文件中添加自定义模型配置：

```bash
# 复制 .env.template 到 .env
cp .env.template .env

# 编辑 .env 文件，添加自定义模型配置
vim .env
```

### 2. 运行应用程序

应用程序会自动解析环境变量并注册自定义模型：

```bash
# 运行主程序
python main.py

# 或者运行测试脚本
python examples/test_custom_models.py
```

### 3. 验证配置

使用测试脚本验证配置是否正确：

```bash
python examples/test_custom_models.py
```

## 编程接口

### CustomModelParser 类

```python
from src.models.custom_model_parser import CustomModelParser

# 创建解析器实例
parser = CustomModelParser()

# 解析环境变量
parser.parse_environment_variables()

# 获取自定义模型配置
config = parser.get_custom_model("my_model")

# 获取智能体模型分配
model_name = parser.get_agent_model("DEEP_RESEARCHER")

# 验证模型配置
is_valid = parser.validate_model_config(config)
```

### ModelManager 类

```python
from src.models import model_manager

# 初始化模型管理器（会自动注册自定义模型）
model_manager.init_models()

# 获取自定义模型
model = model_manager.get_custom_model("my_model")

# 获取智能体分配的模型
model = model_manager.get_agent_model("DEEP_RESEARCHER")

# 列出所有自定义模型
custom_models = model_manager.list_custom_models()

# 重新加载自定义模型
model_manager.reload_custom_models()
```

## 配置验证

系统会自动验证以下内容：

1. **必需参数检查**: 确保所有必需参数都已配置
2. **URL 格式验证**: 验证 API URL 的格式是否正确
3. **模型类型验证**: 验证模型类型是否受支持
4. **配置完整性**: 验证配置是否完整且有效

## 错误处理

系统提供完善的错误处理：

- **配置错误**: 会在日志中记录详细的错误信息
- **模型创建失败**: 会跳过无效配置，继续处理其他模型
- **运行时错误**: 会捕获并记录异常，不影响其他功能

## 日志记录

系统会记录以下信息：

- 自定义模型解析过程
- 模型注册状态
- 配置验证结果
- 错误和警告信息

## 最佳实践

### 1. 命名约定

- 使用有意义的模型名称
- 使用大写字母和下划线分隔单词
- 避免使用特殊字符

### 2. 安全考虑

- 不要在代码中硬编码 API 密钥
- 使用环境变量或配置文件管理敏感信息
- 定期轮换 API 密钥

### 3. 性能优化

- 只配置需要的模型
- 合理设置超时时间
- 使用适当的模型参数

### 4. 错误处理

- 监控日志文件
- 定期检查配置有效性
- 处理 API 限制和错误

## 故障排除

### 常见问题

1. **模型未注册**
   - 检查环境变量是否正确设置
   - 验证必需参数是否完整
   - 查看日志文件中的错误信息

2. **API 调用失败**
   - 验证 API 密钥是否有效
   - 检查网络连接
   - 确认 API 端点是否正确

3. **配置验证失败**
   - 检查 URL 格式是否正确
   - 验证模型类型是否受支持
   - 确认所有必需参数都已设置

### 调试方法

1. **查看日志**
   ```bash
   tail -f logs/dra.log
   ```

2. **运行测试脚本**
   ```bash
   python examples/test_custom_models.py
   ```

3. **检查环境变量**
   ```bash
   env | grep CUSTOM_MODEL
   env | grep AGENT_
   ```

## 示例配置文件

完整的 `.env` 文件示例：

```bash
# 基础配置
PYTHONWARNINGS=ignore
ANONYMIZED_TELEMETRY=false

# OpenAI 配置
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_API_KEY=your-openai-key

# 自定义模型配置
CUSTOM_MODEL_RESEARCH_GPT_BASE_URL=https://api.openai.com/v1
CUSTOM_MODEL_RESEARCH_GPT_API_KEY=your-openai-key
CUSTOM_MODEL_RESEARCH_GPT_MODEL_ID=gpt-4o
CUSTOM_MODEL_RESEARCH_GPT_MODEL_TYPE=openai
CUSTOM_MODEL_RESEARCH_GPT_MAX_TOKENS=4096
CUSTOM_MODEL_RESEARCH_GPT_TEMPERATURE=0.1

CUSTOM_MODEL_PLANNING_CLAUDE_BASE_URL=https://api.anthropic.com
CUSTOM_MODEL_PLANNING_CLAUDE_API_KEY=your-anthropic-key
CUSTOM_MODEL_PLANNING_CLAUDE_MODEL_ID=claude-3-5-sonnet-20241022
CUSTOM_MODEL_PLANNING_CLAUDE_MODEL_TYPE=anthropic

CUSTOM_MODEL_LOCAL_LLAMA_BASE_URL=http://localhost:8000/v1
CUSTOM_MODEL_LOCAL_LLAMA_API_KEY=your-local-key
CUSTOM_MODEL_LOCAL_LLAMA_MODEL_ID=llama-3.1-8b-instruct
CUSTOM_MODEL_LOCAL_LLAMA_MODEL_TYPE=local

# 智能体模型分配
AGENT_DEEP_RESEARCHER_MODEL=research_gpt
AGENT_PLANNING_AGENT_MODEL=planning_claude
AGENT_GENERAL_AGENT_MODEL=local_llama
AGENT_BROWSER_USE_AGENT_MODEL=research_gpt
AGENT_DEEP_ANALYZER_MODEL=planning_claude
```

## 总结

DeepResearchAgent 的自定义模型配置功能提供了强大的灵活性，让您可以根据需要配置各种模型。通过环境变量配置，您可以轻松地为不同的智能体分配不同的模型，实现最佳的性能和效果。

如果您在使用过程中遇到问题，请参考故障排除部分或查看日志文件获取更多信息。
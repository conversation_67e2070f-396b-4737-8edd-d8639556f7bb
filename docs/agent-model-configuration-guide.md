# DeepAgent 智能体模型配置指南

本指南介绍如何为DeepAgent的不同智能体角色配置独立的模型参数。

## 🎯 概述

DeepAgent支持为每个智能体角色单独配置模型，包括：
- **PLANNING_AGENT** - 规划智能体：负责任务分解和协调
- **DEEP_RESEARCHER_AGENT** - 深度研究智能体：负责网络搜索和信息综合
- **DEEP_ANALYZER_AGENT** - 深度分析智能体：负责数据分析和洞察提取
- **BROWSER_USE_AGENT** - 浏览器操作智能体：负责网页交互和数据采集

## 🛠️ 配置方法

### 方法1: Agent角色直接配置 (推荐)

直接为每个Agent角色配置模型参数：

```bash
# 规划智能体配置
PLANNING_AGENT_BASE_URL=https://api.anthropic.com
PLANNING_AGENT_API_KEY=your-claude-api-key
PLANNING_AGENT_MODEL_ID=claude-3-sonnet-20240229
PLANNING_AGENT_MODEL_TYPE=anthropic
PLANNING_AGENT_MAX_TOKENS=4096
PLANNING_AGENT_TEMPERATURE=0.7

# 深度研究智能体配置
DEEP_RESEARCHER_AGENT_BASE_URL=https://api.openai.com/v1
DEEP_RESEARCHER_AGENT_API_KEY=your-openai-api-key
DEEP_RESEARCHER_AGENT_MODEL_ID=gpt-4o
DEEP_RESEARCHER_AGENT_MODEL_TYPE=openai
```

### 方法2: 自定义模型 + 分配 (兼容性)

先定义自定义模型，再分配给Agent：

```bash
# 定义自定义模型
CUSTOM_MODEL_GPT4_BASE_URL=https://api.openai.com/v1
CUSTOM_MODEL_GPT4_API_KEY=your-openai-api-key
CUSTOM_MODEL_GPT4_MODEL_ID=gpt-4o
CUSTOM_MODEL_GPT4_MODEL_TYPE=openai

# 分配给Agent
AGENT_deep_researcher_agent_MODEL=GPT4
```

## 📋 配置参数说明

### 必需参数
| 参数 | 说明 | 示例 |
|------|------|------|
| `BASE_URL` | API服务地址 | `https://api.openai.com/v1` |
| `API_KEY` | API密钥 | `sk-your-api-key` |
| `MODEL_ID` | 模型标识符 | `gpt-4o` |
| `MODEL_TYPE` | 模型类型 | `openai`, `anthropic`, `google` |

### 可选参数 (一般不需要设置)
| 参数 | 说明 | 默认值 |
|------|------|--------|
| `MAX_TOKENS` | 最大输出token数 | 模型默认值 |
| `TEMPERATURE` | 温度参数 (0.0-1.0) | 模型默认值 |
| `TIMEOUT` | 请求超时时间(秒) | 60 |
| `PROXY` | 代理服务器设置 | 无 |

> **💡 提示**: 可选参数通常不需要设置，系统会使用合理的默认值。只有在需要特殊调优时才建议添加这些参数。

### 支持的模型类型
- `openai` - OpenAI兼容API
- `anthropic` - Anthropic Claude
- `google` - Google Gemini
- `azure` - Azure OpenAI
- `huggingface` - HuggingFace Inference
- `local` - 本地部署模型

## 🏗️ 配置示例

### 示例1: 多厂商混合配置
```bash
# 规划用Claude (思维能力强)
PLANNING_AGENT_BASE_URL=https://api.anthropic.com
PLANNING_AGENT_API_KEY=your-claude-key
PLANNING_AGENT_MODEL_ID=claude-3-sonnet-20240229
PLANNING_AGENT_MODEL_TYPE=anthropic

# 研究用GPT-4 (搜索能力强)
DEEP_RESEARCHER_AGENT_BASE_URL=https://api.openai.com/v1
DEEP_RESEARCHER_AGENT_API_KEY=your-openai-key
DEEP_RESEARCHER_AGENT_MODEL_ID=gpt-4o
DEEP_RESEARCHER_AGENT_MODEL_TYPE=openai

# 分析用本地模型 (成本控制)
DEEP_ANALYZER_AGENT_BASE_URL=http://localhost:8000/v1
DEEP_ANALYZER_AGENT_API_KEY=local-key
DEEP_ANALYZER_AGENT_MODEL_ID=qwen2.5-32b-instruct
DEEP_ANALYZER_AGENT_MODEL_TYPE=openai

# 浏览器操作用Gemini (多模态能力)
BROWSER_USE_AGENT_BASE_URL=https://generativelanguage.googleapis.com/v1beta
BROWSER_USE_AGENT_API_KEY=your-google-key
BROWSER_USE_AGENT_MODEL_ID=gemini-1.5-pro
BROWSER_USE_AGENT_MODEL_TYPE=google
```

### 示例2: 全本地部署
```bash
# 所有Agent使用本地模型
PLANNING_AGENT_BASE_URL=http://localhost:8000/v1
PLANNING_AGENT_API_KEY=local-key
PLANNING_AGENT_MODEL_ID=qwen2.5-32b-instruct
PLANNING_AGENT_MODEL_TYPE=openai

DEEP_RESEARCHER_AGENT_BASE_URL=http://localhost:8000/v1
DEEP_RESEARCHER_AGENT_API_KEY=local-key
DEEP_RESEARCHER_AGENT_MODEL_ID=qwen2.5-32b-instruct
DEEP_RESEARCHER_AGENT_MODEL_TYPE=openai

DEEP_ANALYZER_AGENT_BASE_URL=http://localhost:8000/v1
DEEP_ANALYZER_AGENT_API_KEY=local-key
DEEP_ANALYZER_AGENT_MODEL_ID=qwen2.5-32b-instruct
DEEP_ANALYZER_AGENT_MODEL_TYPE=openai

BROWSER_USE_AGENT_BASE_URL=http://localhost:8000/v1
BROWSER_USE_AGENT_API_KEY=local-key
BROWSER_USE_AGENT_MODEL_ID=qwen2.5-32b-instruct
BROWSER_USE_AGENT_MODEL_TYPE=openai
```

### 示例3: 成本优化配置
```bash
# 规划用高质量模型 (关键任务)
PLANNING_AGENT_BASE_URL=https://api.anthropic.com
PLANNING_AGENT_API_KEY=your-claude-key
PLANNING_AGENT_MODEL_ID=claude-3-opus-20240229
PLANNING_AGENT_MODEL_TYPE=anthropic

# 其他Agent用经济型模型
DEEP_RESEARCHER_AGENT_BASE_URL=https://api.openai.com/v1
DEEP_RESEARCHER_AGENT_API_KEY=your-openai-key
DEEP_RESEARCHER_AGENT_MODEL_ID=gpt-4o-mini
DEEP_RESEARCHER_AGENT_MODEL_TYPE=openai

DEEP_ANALYZER_AGENT_BASE_URL=https://api.openai.com/v1
DEEP_ANALYZER_AGENT_API_KEY=your-openai-key
DEEP_ANALYZER_AGENT_MODEL_ID=gpt-4o-mini
DEEP_ANALYZER_AGENT_MODEL_TYPE=openai

BROWSER_USE_AGENT_BASE_URL=http://localhost:8000/v1
BROWSER_USE_AGENT_API_KEY=local-key
BROWSER_USE_AGENT_MODEL_ID=qwen2.5-7b-instruct
BROWSER_USE_AGENT_MODEL_TYPE=openai
```

## 🔄 配置优先级

系统按以下优先级选择模型：

1. **Agent直接配置** - `{AGENT_NAME}_{PARAM}`
2. **传统模型分配** - `AGENT_{agent_name}_MODEL`
3. **配置文件指定** - `config.py`中的`model_id`
4. **系统默认** - `claude-3.7-sonnet-thinking`

## 🚀 使用步骤

### 1. 复制配置模板
```bash
cp .env.example .env
```

### 2. 编辑配置文件
根据您的需求修改`.env`文件中的配置

### 3. 验证配置
启动DeepAgent，查看日志确认配置生效：
```bash
python main.py
```

在日志中应该看到类似信息：
```
成功解析Agent直接配置: planning_agent
为Agent planning_agent 使用模型: agent_planning_agent_direct
```

### 4. 测试功能
运行测试任务确认各Agent使用正确的模型

## 🔧 故障排除

### 常见问题

**Q: Agent没有使用我配置的模型？**
A: 检查环境变量名称是否正确，确保Agent名称大写且参数名正确

**Q: 提示模型未注册？**
A: 确保必需参数(BASE_URL, API_KEY, MODEL_ID, MODEL_TYPE)都已配置

**Q: API调用失败？**
A: 检查API密钥是否有效，BASE_URL是否正确

**Q: 本地模型连接失败？**
A: 确认本地模型服务已启动且端口正确

### 调试命令

查看当前配置：
```python
from src.models import model_manager
model_manager.init_models()
print(model_manager.get_all_agent_configs())
```

## 📚 更多资源

- [DeepAgent 项目文档](../README.md)
- [模型提供商API文档](#)
- [本地模型部署指南](#)
- [性能优化建议](#)

---

如有问题，请查看项目Issues或提交新的Issue。
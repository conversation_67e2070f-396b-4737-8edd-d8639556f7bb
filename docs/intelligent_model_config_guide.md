# DeepAgent 智能模型配置系统

## 概述

DeepAgent 智能模型配置系统提供了一个灵活且智能的方式来为不同的智能体角色配置最适合的模型。系统通过角色特征分析、智能推荐算法和用户自定义覆盖，确保每个智能体都能使用最适合其任务的模型。

## 🎯 核心特性

### 1. 角色驱动配置
- **角色特征定义**: 每个智能体角色都有详细的特征配置，包括主要能力需求、任务复杂度、响应风格等
- **智能推荐**: 基于角色特征自动推荐最适合的模型
- **能力匹配**: 根据模型能力评分和角色需求进行智能匹配

### 2. 灵活的配置层次
```
用户指定覆盖 > 角色智能推荐 > 传统直接分配 > 默认配置
```

### 3. 多种配置方式
- **环境变量配置**: 通过环境变量设置模型和覆盖配置
- **配置文件**: JSON格式的持久化配置文件
- **代码配置**: 通过API动态设置和修改配置
- **命令行工具**: 提供便捷的命令行管理接口

## 🏗️ 架构设计

### 核心组件

1. **AgentRoleConfigManager**: 角色配置管理器
   - 管理角色配置文件和模型配置文件
   - 提供智能推荐算法
   - 处理用户覆盖设置

2. **CustomModelParser**: 自定义模型解析器（增强版）
   - 解析环境变量中的模型配置
   - 集成角色配置管理器
   - 处理传统的智能体-模型直接分配

3. **IntelligentModelSelector**: 智能模型选择器
   - 统一的模型选择接口
   - 整合多种配置来源
   - 提供选择结果和详细信息

## 📋 使用指南

### 1. 基本使用

```python
from src.models.intelligent_model_selector import get_model_selector

# 获取模型选择器
selector = get_model_selector()

# 为智能体选择模型
result = selector.select_model_for_agent("planning_agent")
print(f"推荐模型: {result.selected_model}")
print(f"选择方式: {result.selection_method}")
```

### 2. 环境变量配置

#### 2.1 自定义模型配置
```bash
# 配置自定义OpenAI模型
CUSTOM_MODEL_MY_GPT4_BASE_URL=https://api.openai.com/v1
CUSTOM_MODEL_MY_GPT4_API_KEY=your_openai_api_key
CUSTOM_MODEL_MY_GPT4_MODEL_ID=gpt-4-1106-preview
CUSTOM_MODEL_MY_GPT4_MODEL_TYPE=openai

# 配置自定义Anthropic模型
CUSTOM_MODEL_MY_CLAUDE_BASE_URL=https://api.anthropic.com
CUSTOM_MODEL_MY_CLAUDE_API_KEY=your_anthropic_api_key
CUSTOM_MODEL_MY_CLAUDE_MODEL_ID=claude-3-opus-20240229
CUSTOM_MODEL_MY_CLAUDE_MODEL_TYPE=anthropic
```

#### 2.2 智能体模型分配
```bash
# 传统方式：直接分配模型
AGENT_PLANNING_AGENT_MODEL=claude-3.7-sonnet-thinking
AGENT_BROWSER_USE_AGENT_MODEL=gpt-4.1

# 用户覆盖：会保存到配置文件
AGENT_PLANNING_AGENT_MODEL_OVERRIDE=MY_CLAUDE
```

#### 2.3 系统配置
```bash
# 启用角色推荐系统
ENABLE_ROLE_BASED_MODEL_RECOMMENDATION=true

# 性能偏好
AGENT_PERFORMANCE_PRIORITY=balanced  # speed, balanced, quality
AGENT_COST_SENSITIVITY=medium        # low, medium, high
```

### 3. 配置文件使用

#### 3.1 用户配置文件 (`src/config/agent_roles/user_config.json`)
```json
{
  "agent_model_overrides": {
    "planning_agent": "claude-3.7-sonnet-thinking",
    "deep_researcher_agent": "claude-3.7-sonnet-thinking"
  },
  "global_preferences": {
    "performance_priority": "balanced",
    "cost_sensitivity": "medium",
    "context_length_preference": "large"
  }
}
```

### 4. 代码配置示例

#### 4.1 设置用户覆盖
```python
from src.models.intelligent_model_selector import get_model_selector

selector = get_model_selector()

# 设置智能体使用特定模型
selector.set_agent_model("planning_agent", "claude-3.7-sonnet-thinking")

# 移除覆盖设置，恢复推荐模型
selector.remove_agent_model("planning_agent")
```

#### 4.2 添加自定义角色
```python
from src.models.agent_role_config import (
    get_role_config_manager, 
    AgentRoleProfile,
    TaskComplexity,
    ResponseStyle,
    ModelCapability
)

role_manager = get_role_config_manager()

# 定义新角色
custom_role = AgentRoleProfile(
    role_name="code_reviewer_agent",
    description="代码审查智能体",
    primary_capabilities=[
        ModelCapability.ANALYSIS,
        ModelCapability.CODING,
        ModelCapability.REASONING
    ],
    task_complexity=TaskComplexity.EXPERT,
    response_style=ResponseStyle.ANALYTICAL,
    context_length_requirement=20000,
    reasoning_depth="deep",
    domain_expertise=["code_review", "software_engineering"],
    performance_priority="quality"
)

# 添加角色
role_manager.add_custom_role(custom_role)

# 获取推荐
recommendation = role_manager.recommend_model_for_role("code_reviewer_agent")
```

#### 4.3 添加自定义模型
```python
from src.models.custom_model_parser import CustomModelConfig, ModelType

# 创建自定义模型配置
custom_model = CustomModelConfig(
    name="my_local_llm",
    base_url="http://localhost:8000/v1",
    api_key="local_key",
    model_id="llama-2-70b-chat",
    model_type=ModelType.LOCAL,
    additional_params={
        "max_tokens": 4096,
        "temperature": 0.7
    }
)

# 添加到选择器
selector.add_custom_model(custom_model)
```

### 5. 命令行工具

系统提供了完整的命令行管理工具：

```bash
# 查看所有智能体配置
python src/models/model_config_cli.py list

# 显示所有可用模型
python src/models/model_config_cli.py models

# 为智能体推荐模型
python src/models/model_config_cli.py recommend planning_agent

# 设置智能体使用特定模型
python src/models/model_config_cli.py set planning_agent claude-3.7-sonnet-thinking

# 移除智能体的模型设置
python src/models/model_config_cli.py remove planning_agent

# 测试智能体配置
python src/models/model_config_cli.py test planning_agent

# 生成配置报告
python src/models/model_config_cli.py report --output config_report.json

# 重新加载配置
python src/models/model_config_cli.py reload
```

## 🧠 智能推荐算法

### 推荐评分算法

推荐系统使用多维度评分算法来为每个角色推荐最适合的模型：

1. **主要能力匹配 (权重40%)**
   - 基于角色的主要能力需求和模型的能力评分进行匹配
   - 计算平均能力匹配度

2. **任务复杂度匹配 (权重25%)**
   - 根据角色的任务复杂度和模型的推理能力进行匹配
   - 确保模型有足够的推理能力处理任务

3. **上下文长度需求 (权重15%)**
   - 检查模型的最大上下文长度是否满足角色需求
   - 确保能够处理长文档和复杂对话

4. **性能优先级匹配 (权重10%)**
   - 根据角色的性能优先级（速度/质量/平衡）选择合适的模型
   - 平衡速度和质量需求

5. **Token效率匹配 (权重10%)**
   - 考虑成本因素和token使用效率
   - 为预算敏感的应用选择合适的模型

### 推荐置信度

推荐结果包含置信度评分（0-1），表示推荐的可靠性：
- **0.9+**: 非常匹配，强烈推荐
- **0.7-0.9**: 良好匹配，推荐使用
- **0.5-0.7**: 基本匹配，可以使用
- **0.5以下**: 匹配度较低，建议查看备选方案

## 🔧 高级配置

### 1. 自定义模型能力评分

可以通过环境变量自定义模型的能力评分：

```bash
# 自定义模型能力评分
MODEL_MY_GPT4_REASONING_SCORE=9.0
MODEL_MY_GPT4_CODING_SCORE=9.5
MODEL_MY_GPT4_ANALYSIS_SCORE=8.5
```

### 2. 角色配置自定义

```python
# 修改现有角色的配置
role_manager = get_role_config_manager()
planning_role = role_manager.role_profiles["planning_agent"]
planning_role.performance_priority = "speed"  # 修改性能偏好
```

### 3. 运行时动态调整

```python
# 运行时动态切换模型
selector = get_model_selector()

# 临时设置（不保存到文件）
selector.set_agent_model("planning_agent", "gpt-4.1", persistent=False)

# 重新加载配置
selector.reload_configurations()
```

## 📊 监控和调试

### 1. 配置报告

```python
# 获取完整配置报告
report = selector.get_configuration_report()
print(json.dumps(report, indent=2))
```

### 2. 推荐详情

```python
# 获取推荐详情
details = selector.get_model_recommendation_details("planning_agent")
print(f"推荐置信度: {details['confidence_score']}")
print(f"推荐理由: {details['reasoning']}")
```

### 3. 调试模式

```bash
# 启用调试模式
ROLE_RECOMMENDATION_DEBUG=true
LOG_LEVEL=DEBUG
```

## 🚀 最佳实践

### 1. 配置管理
- 使用环境变量进行基础配置
- 使用配置文件进行持久化设置
- 定期备份用户配置文件

### 2. 模型选择
- 对于计划和分析任务，优先选择推理能力强的模型
- 对于快速交互任务，优先选择速度快的模型
- 根据成本预算合理选择模型

### 3. 性能优化
- 定期审查模型使用情况和成本
- 根据实际效果调整模型配置
- 使用推荐系统的备选方案进行A/B测试

### 4. 安全考虑
- 妥善保管API密钥
- 使用环境变量而非硬编码配置
- 定期更新模型配置和安全设置

## 🔗 相关文件

- `src/models/agent_role_config.py`: 角色配置管理核心
- `src/models/custom_model_parser.py`: 自定义模型解析器
- `src/models/intelligent_model_selector.py`: 智能模型选择器
- `src/models/model_config_cli.py`: 命令行管理工具
- `examples/test_intelligent_model_config.py`: 使用示例
- `src/config/agent_roles/user_config.json`: 用户配置文件
- `.env.example`: 环境变量配置示例

## 📝 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持基于角色的智能推荐
- 多层级配置系统
- 命令行管理工具
- 完整的文档和示例
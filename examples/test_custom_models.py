#!/usr/bin/env python3
"""
测试自定义模型配置功能
"""
import os
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到路径
root = str(Path(__file__).resolve().parents[1])
sys.path.append(root)

from dotenv import load_dotenv
load_dotenv(verbose=True)

from src.models import model_manager
from src.models.custom_model_parser import CustomModelParser
from src.logger import logger


def test_custom_model_parser():
    """测试自定义模型解析器"""
    print("=" * 50)
    print("测试自定义模型解析器")
    print("=" * 50)
    
    # 创建解析器实例
    parser = CustomModelParser()
    
    # 解析环境变量
    parser.parse_environment_variables()
    
    # 显示配置摘要
    summary = parser.get_config_summary()
    print(f"配置摘要: {summary}")
    
    # 列出自定义模型
    custom_models = parser.list_custom_models()
    print(f"自定义模型: {custom_models}")
    
    # 列出智能体分配
    agent_assignments = parser.list_agent_assignments()
    print(f"智能体分配: {agent_assignments}")
    
    # 验证模型配置
    for model_name in custom_models:
        config = parser.get_custom_model(model_name)
        if config:
            is_valid = parser.validate_model_config(config)
            print(f"模型 {model_name} 配置验证: {'通过' if is_valid else '失败'}")
            if is_valid:
                print(f"  - 名称: {config.name}")
                print(f"  - 类型: {config.model_type.value}")
                print(f"  - 模型ID: {config.model_id}")
                print(f"  - 基础URL: {config.base_url}")
                print(f"  - 额外参数: {config.additional_params}")
    
    return parser


def test_model_manager():
    """测试模型管理器"""
    print("\n" + "=" * 50)
    print("测试模型管理器")
    print("=" * 50)
    
    # 初始化模型管理器
    model_manager.init_models(use_local_proxy=False)
    
    # 显示所有注册的模型
    print(f"已注册的模型总数: {len(model_manager.registed_models)}")
    print("所有模型:")
    for model_name in model_manager.registed_models.keys():
        print(f"  - {model_name}")
    
    # 显示自定义模型
    custom_models = model_manager.list_custom_models()
    print(f"\n自定义模型总数: {len(custom_models)}")
    print("自定义模型:")
    for model_name in custom_models:
        print(f"  - {model_name}")
    
    # 显示智能体分配
    agent_assignments = model_manager.list_agent_assignments()
    print(f"\n智能体分配:")
    for agent_name, model_name in agent_assignments.items():
        model = model_manager.get_agent_model(agent_name)
        status = "已分配" if model else "未找到"
        print(f"  - {agent_name}: {model_name} ({status})")
    
    return model_manager


def test_model_validation():
    """测试模型验证"""
    print("\n" + "=" * 50)
    print("测试模型验证")
    print("=" * 50)
    
    # 测试自定义模型配置验证
    custom_models = model_manager.list_custom_models()
    for model_name in custom_models:
        is_valid = model_manager.validate_custom_model_config(model_name)
        print(f"模型 {model_name} 配置验证: {'通过' if is_valid else '失败'}")
    
    # 测试模型信息获取
    for model_name in custom_models:
        info = model_manager.get_model_info(model_name)
        if info:
            print(f"模型 {model_name} 信息: {info}")


def test_agent_model_assignment():
    """测试智能体模型分配"""
    print("\n" + "=" * 50)
    print("测试智能体模型分配")
    print("=" * 50)
    
    # 测试各个智能体的模型分配
    agents = [
        "DEEP_RESEARCHER",
        "GENERAL_AGENT", 
        "PLANNING_AGENT",
        "BROWSER_USE_AGENT",
        "DEEP_ANALYZER"
    ]
    
    for agent_name in agents:
        model = model_manager.get_agent_model(agent_name)
        if model:
            print(f"智能体 {agent_name} 已分配模型: {type(model).__name__}")
        else:
            print(f"智能体 {agent_name} 未分配模型")


async def test_model_functionality():
    """测试模型功能"""
    print("\n" + "=" * 50)
    print("测试模型功能")
    print("=" * 50)
    
    # 测试自定义模型是否可以正常调用
    custom_models = model_manager.list_custom_models()
    
    if not custom_models:
        print("没有找到自定义模型，跳过功能测试")
        return
    
    # 选择第一个自定义模型进行测试
    test_model_name = custom_models[0]
    model = model_manager.get_custom_model(test_model_name)
    
    if model:
        print(f"测试模型: {test_model_name}")
        print(f"模型类型: {type(model).__name__}")
        
        # 尝试获取模型属性
        try:
            if hasattr(model, 'model_id'):
                print(f"模型ID: {model.model_id}")
            if hasattr(model, 'api_base'):
                print(f"API基础URL: {model.api_base}")
            if hasattr(model, 'kwargs'):
                print(f"模型参数: {model.kwargs}")
                
            print("模型基本属性检查通过")
        except Exception as e:
            print(f"检查模型属性时出错: {e}")


def main():
    """主函数"""
    print("开始测试自定义模型配置功能")
    
    try:
        # 测试自定义模型解析器
        parser = test_custom_model_parser()
        
        # 测试模型管理器
        manager = test_model_manager()
        
        # 测试模型验证
        test_model_validation()
        
        # 测试智能体模型分配
        test_agent_model_assignment()
        
        # 测试模型功能
        asyncio.run(test_model_functionality())
        
        print("\n" + "=" * 50)
        print("所有测试完成！")
        print("=" * 50)
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
#!/usr/bin/env python3
"""
智能模型配置系统测试示例
演示如何使用角色配置和智能推荐系统
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.models.intelligent_model_selector import get_model_selector
from src.models.agent_role_config import (
    get_role_config_manager, 
    AgentRoleProfile, 
    ModelProfile,
    TaskComplexity, 
    ResponseStyle, 
    ModelCapability
)
from src.models.custom_model_parser import CustomModelConfig, ModelType


def demo_basic_usage():
    """演示基本使用方法"""
    print("\n🎯 基本使用演示")
    print("=" * 50)
    
    # 获取模型选择器
    selector = get_model_selector()
    
    # 为不同智能体选择模型
    agents = ["planning_agent", "deep_researcher_agent", "deep_analyzer_agent", "browser_use_agent"]
    
    for agent_name in agents:
        result = selector.select_model_for_agent(agent_name)
        print(f"📋 {agent_name}:")
        print(f"  ├─ 选中模型: {result.selected_model}")
        print(f"  ├─ 选择方式: {result.selection_method}")
        
        if result.recommendation:
            print(f"  ├─ 推荐置信度: {result.recommendation.confidence_score:.2f}")
            print(f"  └─ 推荐理由: {result.recommendation.reasoning}")
        else:
            print(f"  └─ 无推荐信息")


def demo_custom_role():
    """演示添加自定义角色"""
    print("\n🔧 自定义角色演示")
    print("=" * 50)
    
    role_manager = get_role_config_manager()
    
    # 定义一个新的角色
    custom_role = AgentRoleProfile(
        role_name="code_reviewer_agent",
        description="代码审查智能体，专门进行代码质量检查和优化建议",
        primary_capabilities=[
            ModelCapability.ANALYSIS,
            ModelCapability.CODING,
            ModelCapability.REASONING
        ],
        task_complexity=TaskComplexity.EXPERT,
        response_style=ResponseStyle.ANALYTICAL,
        context_length_requirement=20000,
        reasoning_depth="deep",
        domain_expertise=["code_review", "software_engineering", "best_practices"],
        performance_priority="quality",
        token_efficiency="balanced"
    )
    
    # 添加自定义角色
    role_manager.add_custom_role(custom_role)
    
    # 获取推荐
    recommendation = role_manager.recommend_model_for_role("code_reviewer_agent")
    
    print(f"🤖 新角色: code_reviewer_agent")
    print(f"  ├─ 推荐模型: {recommendation.model_name}")
    print(f"  ├─ 置信度: {recommendation.confidence_score:.2f}")
    print(f"  └─ 推荐理由: {recommendation.reasoning}")


def demo_custom_model():
    """演示添加自定义模型"""
    print("\n⚙️ 自定义模型演示")
    print("=" * 50)
    
    selector = get_model_selector()
    
    # 创建自定义模型配置
    custom_model = CustomModelConfig(
        name="my_local_llm",
        base_url="http://localhost:8000/v1",
        api_key="local_key",
        model_id="llama-2-70b-chat",
        model_type=ModelType.LOCAL,
        additional_params={
            "max_tokens": 4096,
            "temperature": 0.7
        }
    )
    
    # 添加自定义模型
    try:
        selector.add_custom_model(custom_model)
        print(f"✅ 成功添加自定义模型: {custom_model.name}")
        
        # 获取模型详情
        details = selector.get_model_details(custom_model.name)
        if details:
            print(f"📋 模型详情:")
            print(f"  ├─ 类型: {details['custom_config']['model_type']}")
            print(f"  ├─ 模型ID: {details['custom_config']['model_id']}")
            print(f"  └─ Base URL: {details['custom_config']['base_url']}")
        
    except Exception as e:
        print(f"❌ 添加模型失败: {e}")


def demo_user_overrides():
    """演示用户覆盖配置"""
    print("\n👤 用户覆盖演示")
    print("=" * 50)
    
    selector = get_model_selector()
    
    # 设置用户覆盖
    agent_name = "planning_agent"
    override_model = "claude-3-haiku"
    
    print(f"📝 设置覆盖: {agent_name} -> {override_model}")
    selector.set_agent_model(agent_name, override_model, persistent=False)
    
    # 验证覆盖生效
    result = selector.select_model_for_agent(agent_name)
    print(f"✅ 覆盖生效:")
    print(f"  ├─ 选中模型: {result.selected_model}")
    print(f"  └─ 选择方式: {result.selection_method}")
    
    # 移除覆盖
    print(f"\n🔄 移除覆盖配置")
    selector.remove_agent_model(agent_name, persistent=False)
    
    # 验证恢复到推荐模型
    result = selector.select_model_for_agent(agent_name)
    print(f"🔄 恢复推荐:")
    print(f"  ├─ 选中模型: {result.selected_model}")
    print(f"  └─ 选择方式: {result.selection_method}")


def demo_configuration_report():
    """演示配置报告"""
    print("\n📊 配置报告演示")
    print("=" * 50)
    
    selector = get_model_selector()
    report = selector.get_configuration_report()
    
    print(f"🔍 系统状态:")
    status = report["selector_status"]
    print(f"  ├─ 角色推荐: {'启用' if status['role_based_enabled'] else '禁用'}")
    print(f"  ├─ 自定义模型: {status['custom_models_count']} 个")
    print(f"  └─ 智能体分配: {status['agent_assignments_count']} 个")
    
    print(f"\n🤖 智能体配置:")
    for agent_name, config in report["agent_selections"].items():
        print(f"  ├─ {agent_name}: {config['selected_model']} ({config['selection_method']})")
    
    print(f"\n🎯 可用模型:")
    for model in report["available_models"]:
        print(f"  ├─ {model}")


def demo_environment_integration():
    """演示环境变量集成"""
    print("\n🌍 环境变量集成演示")
    print("=" * 50)
    
    # 模拟设置环境变量
    test_vars = {
        "AGENT_PLANNING_AGENT_MODEL_OVERRIDE": "claude-3.7-sonnet-thinking",
        "AGENT_BROWSER_USE_AGENT_MODEL_OVERRIDE": "gpt-3.5-turbo",
        "ENABLE_ROLE_BASED_MODEL_RECOMMENDATION": "true"
    }
    
    # 备份原始环境变量
    original_vars = {}
    for key in test_vars:
        original_vars[key] = os.environ.get(key)
        os.environ[key] = test_vars[key]
    
    try:
        # 重新创建选择器以读取新的环境变量
        selector = get_model_selector()
        selector.reload_configurations()
        
        print("📝 模拟环境变量:")
        for key, value in test_vars.items():
            print(f"  ├─ {key} = {value}")
        
        print(f"\n🔍 配置读取结果:")
        summary = selector.get_agent_selection_summary()
        for agent_name, config in summary.items():
            if "override" in config['selection_method'] or config.get('user_override'):
                print(f"  ├─ {agent_name}: {config['selected_model']} (环境变量覆盖)")
        
    finally:
        # 恢复原始环境变量
        for key, original_value in original_vars.items():
            if original_value is None:
                os.environ.pop(key, None)
            else:
                os.environ[key] = original_value


def main():
    """主演示函数"""
    print("🚀 DeepAgent 智能模型配置系统演示")
    print("=" * 60)
    
    try:
        demo_basic_usage()
        demo_custom_role()
        demo_custom_model()
        demo_user_overrides()
        demo_configuration_report()
        demo_environment_integration()
        
        print("\n✅ 演示完成！")
        print("\n💡 提示：")
        print("  - 使用 python src/models/model_config_cli.py --help 查看命令行工具")
        print("  - 查看 .env.example 了解环境变量配置")
        print("  - 自定义配置文件位于 src/config/agent_roles/")
        
    except Exception as e:
        print(f"\n❌ 演示过程出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
#!/usr/bin/env python3
"""
智能模型配置系统使用示例
展示如何在实际项目中使用智能模型配置
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.models.models import ModelManager
from src.models.intelligent_model_selector import get_model_selector
from src.models.agent_role_config import get_role_config_manager


def example_basic_agent_model_selection():
    """示例1: 基础智能体模型选择"""
    print("\n📋 示例1: 基础智能体模型选择")
    print("=" * 50)
    
    # 获取模型管理器
    model_manager = ModelManager()
    model_manager.init_models()
    
    # 为不同智能体选择模型
    agents = ["planning_agent", "deep_researcher_agent", "deep_analyzer_agent", "browser_use_agent"]
    
    for agent_name in agents:
        # 使用智能选择
        selected_model = model_manager.get_intelligent_model_for_agent(agent_name)
        
        # 获取选择详情
        details = model_manager.get_model_selection_details(agent_name)
        
        print(f"\n🤖 {agent_name}:")
        print(f"  ├─ 选中模型: {selected_model}")
        
        if details:
            print(f"  ├─ 选择方式: {details['selection_method']}")
            if details.get('recommendation'):
                rec = details['recommendation']
                print(f"  ├─ 推荐置信度: {rec['confidence_score']:.2f}")
                print(f"  └─ 推荐理由: {rec['reasoning']}")
            else:
                print(f"  └─ 无推荐信息")


def example_custom_role_configuration():
    """示例2: 自定义角色配置"""
    print("\n🔧 示例2: 自定义角色配置")
    print("=" * 50)
    
    from src.models.agent_role_config import (
        AgentRoleProfile, 
        TaskComplexity, 
        ResponseStyle, 
        ModelCapability
    )
    
    role_manager = get_role_config_manager()
    
    # 创建一个代码审查智能体角色
    code_reviewer_role = AgentRoleProfile(
        role_name="code_reviewer_agent",
        description="专门进行代码审查的智能体，注重代码质量和最佳实践",
        primary_capabilities=[
            ModelCapability.ANALYSIS,
            ModelCapability.CODING, 
            ModelCapability.REASONING
        ],
        task_complexity=TaskComplexity.EXPERT,
        response_style=ResponseStyle.ANALYTICAL,
        context_length_requirement=24000,
        reasoning_depth="deep",
        domain_expertise=["code_review", "software_engineering", "best_practices"],
        performance_priority="quality",
        token_efficiency="balanced"
    )
    
    # 添加角色
    role_manager.add_custom_role(code_reviewer_role)
    
    # 获取推荐
    recommendation = role_manager.recommend_model_for_role("code_reviewer_agent")
    
    print(f"🎯 新角色配置结果:")
    print(f"  ├─ 角色名称: code_reviewer_agent")
    print(f"  ├─ 推荐模型: {recommendation.model_name}")
    print(f"  ├─ 置信度: {recommendation.confidence_score:.2f}")
    print(f"  └─ 推荐理由: {recommendation.reasoning}")
    
    if recommendation.alternatives:
        print(f"  备选模型: {', '.join(recommendation.alternatives)}")


def example_environment_based_configuration():
    """示例3: 基于环境变量的配置"""
    print("\n🌍 示例3: 环境变量配置")
    print("=" * 50)
    
    # 设置测试环境变量
    test_env_vars = {
        "CUSTOM_MODEL_MY_LOCAL_LLM_BASE_URL": "http://localhost:8000/v1",
        "CUSTOM_MODEL_MY_LOCAL_LLM_API_KEY": "local_key",
        "CUSTOM_MODEL_MY_LOCAL_LLM_MODEL_ID": "llama-2-70b-chat",
        "CUSTOM_MODEL_MY_LOCAL_LLM_MODEL_TYPE": "local",
        "AGENT_PLANNING_AGENT_MODEL_OVERRIDE": "MY_LOCAL_LLM"
    }
    
    # 备份原始环境变量
    original_vars = {}
    for key in test_env_vars:
        original_vars[key] = os.environ.get(key)
        os.environ[key] = test_env_vars[key]
    
    try:
        # 重新初始化模型管理器
        model_manager = ModelManager()
        model_manager.init_models()
        
        print("📝 设置的环境变量:")
        for key, value in test_env_vars.items():
            print(f"  ├─ {key} = {value}")
        
        # 测试配置读取
        selected_model = model_manager.get_intelligent_model_for_agent("planning_agent")
        print(f"\n✅ 配置读取结果:")
        print(f"  └─ planning_agent 选中模型: {selected_model}")
        
        # 获取自定义模型信息
        custom_config = model_manager.custom_model_parser.get_custom_model("MY_LOCAL_LLM")
        if custom_config:
            print(f"\n🔧 自定义模型信息:")
            print(f"  ├─ 名称: {custom_config.name}")
            print(f"  ├─ 类型: {custom_config.model_type.value}")
            print(f"  ├─ Base URL: {custom_config.base_url}")
            print(f"  └─ Model ID: {custom_config.model_id}")
        
    finally:
        # 恢复原始环境变量
        for key, original_value in original_vars.items():
            if original_value is None:
                os.environ.pop(key, None)
            else:
                os.environ[key] = original_value


def example_dynamic_model_switching():
    """示例4: 动态模型切换"""
    print("\n🔄 示例4: 动态模型切换")
    print("=" * 50)
    
    model_manager = ModelManager()
    model_manager.init_models()
    
    agent_name = "planning_agent"
    
    # 获取当前模型
    current_model = model_manager.get_intelligent_model_for_agent(agent_name)
    print(f"📋 当前模型: {current_model}")
    
    # 设置新模型偏好
    new_model = "claude-3-haiku"
    success = model_manager.set_agent_model_preference(agent_name, new_model, persistent=False)
    
    if success:
        print(f"✅ 成功设置新模型偏好: {new_model}")
        
        # 验证切换结果
        updated_model = model_manager.get_intelligent_model_for_agent(agent_name)
        print(f"🔄 切换后模型: {updated_model}")
        
        # 获取详细信息
        details = model_manager.get_model_selection_details(agent_name)
        if details:
            print(f"📊 选择详情:")
            print(f"  ├─ 选择方式: {details['selection_method']}")
            print(f"  └─ 备用配置: {'是' if details['fallback_used'] else '否'}")
    else:
        print("❌ 模型切换失败")


def example_configuration_monitoring():
    """示例5: 配置监控和报告"""
    print("\n📊 示例5: 配置监控和报告")
    print("=" * 50)
    
    model_manager = ModelManager()
    model_manager.init_models()
    
    # 获取所有智能体配置摘要
    all_configs = model_manager.get_all_agent_model_configs()
    
    print("🤖 所有智能体配置摘要:")
    for agent_name, config in all_configs.items():
        print(f"\n📋 {agent_name}:")
        print(f"  ├─ 选中模型: {config['selected_model']}")
        print(f"  ├─ 选择方式: {config['selection_method']}")
        print(f"  ├─ 自定义配置: {'有' if config['has_custom_config'] else '无'}")
        
        if config['recommendation_confidence']:
            print(f"  ├─ 推荐置信度: {config['recommendation_confidence']:.2f}")
        
        print(f"  └─ 备用配置: {'是' if config['fallback_used'] else '否'}")
    
    # 获取可用模型列表
    selector = get_model_selector()
    available_models = selector.get_all_available_models()
    
    print(f"\n🔧 可用模型列表 ({len(available_models)} 个):")
    for model_name, info in available_models.items():
        print(f"  ├─ {model_name} ({info['source']})")


def example_best_practices():
    """示例6: 最佳实践演示"""
    print("\n💡 示例6: 最佳实践")
    print("=" * 50)
    
    # 1. 错误处理和备用方案
    print("1️⃣ 错误处理和备用方案:")
    
    model_manager = ModelManager()
    model_manager.init_models()
    
    # 尝试获取不存在的智能体模型，使用备用方案
    unknown_agent = "unknown_agent"
    fallback_model = "claude-3.7-sonnet-thinking"
    
    selected_model = model_manager.get_intelligent_model_for_agent(
        unknown_agent, 
        fallback_model=fallback_model
    )
    print(f"  └─ 未知智能体 {unknown_agent} 使用备用模型: {selected_model}")
    
    # 2. 配置验证
    print("\n2️⃣ 配置验证:")
    
    # 验证模型配置
    test_models = ["claude-3.7-sonnet-thinking", "gpt-4.1", "nonexistent-model"]
    for model_name in test_models:
        is_valid = model_manager.validate_custom_model_config(model_name)
        model_info = model_manager.get_model_info(model_name)
        
        status = "✅ 有效" if (is_valid or model_info) else "❌ 无效"
        print(f"  ├─ {model_name}: {status}")
    
    # 3. 性能监控
    print("\n3️⃣ 性能监控建议:")
    print("  ├─ 定期检查模型推荐置信度")
    print("  ├─ 监控模型使用频率和成本")
    print("  ├─ 基于实际效果调整角色配置")
    print("  └─ 使用A/B测试比较不同模型效果")
    
    # 4. 安全考虑
    print("\n4️⃣ 安全考虑:")
    print("  ├─ API密钥通过环境变量管理")
    print("  ├─ 定期轮换API密钥")
    print("  ├─ 限制模型访问权限") 
    print("  └─ 监控异常模型调用")


def main():
    """主函数"""
    print("🚀 DeepAgent 智能模型配置使用示例")
    print("=" * 60)
    
    try:
        example_basic_agent_model_selection()
        example_custom_role_configuration()
        example_environment_based_configuration()
        example_dynamic_model_switching()
        example_configuration_monitoring()
        example_best_practices()
        
        print("\n✅ 所有示例运行完成！")
        
        print("\n📚 相关资源:")
        print("  ├─ 详细文档: docs/intelligent_model_config_guide.md")
        print("  ├─ 命令行工具: python src/models/model_config_cli.py --help")
        print("  ├─ 配置示例: .env.example")
        print("  └─ 测试代码: examples/test_intelligent_model_config.py")
        
    except Exception as e:
        print(f"\n❌ 示例运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
# DeepResearchAgent 自定义模型配置示例
# 复制此文件为 .env 并填入您的实际配置

# 基础配置
PYTHONWARNINGS=ignore
ANONYMIZED_TELEMETRY=false

# OpenAI API 配置（备用）
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_API_KEY=your-openai-api-key

# Anthropic API 配置（备用）
ANTHROPIC_API_BASE=https://api.anthropic.com
ANTHROPIC_API_KEY=your-anthropic-api-key

# Google API 配置（备用）
GOOGLE_API_BASE=https://generativelanguage.googleapis.com
GOOGLE_API_KEY=your-google-api-key

# === 自定义模型配置示例 ===

# 示例 1：自定义 OpenAI 兼容模型（用于研究任务）
CUSTOM_MODEL_RESEARCH_GPT_BASE_URL=https://api.openai.com/v1
CUSTOM_MODEL_RESEARCH_GPT_API_KEY=your-openai-api-key
CUSTOM_MODEL_RESEARCH_GPT_MODEL_ID=gpt-4o
CUSTOM_MODEL_RESEARCH_GPT_MODEL_TYPE=openai
CUSTOM_MODEL_RESEARCH_GPT_MAX_TOKENS=4096
CUSTOM_MODEL_RESEARCH_GPT_TEMPERATURE=0.1

# 示例 2：自定义 Anthropic 模型（用于规划任务）
CUSTOM_MODEL_PLANNING_CLAUDE_BASE_URL=https://api.anthropic.com
CUSTOM_MODEL_PLANNING_CLAUDE_API_KEY=your-anthropic-api-key
CUSTOM_MODEL_PLANNING_CLAUDE_MODEL_ID=claude-3-5-sonnet-20241022
CUSTOM_MODEL_PLANNING_CLAUDE_MODEL_TYPE=anthropic
CUSTOM_MODEL_PLANNING_CLAUDE_MAX_TOKENS=4096
CUSTOM_MODEL_PLANNING_CLAUDE_TEMPERATURE=0.3

# 示例 3：本地部署的模型（用于通用任务）
CUSTOM_MODEL_LOCAL_LLAMA_BASE_URL=http://localhost:8000/v1
CUSTOM_MODEL_LOCAL_LLAMA_API_KEY=your-local-api-key
CUSTOM_MODEL_LOCAL_LLAMA_MODEL_ID=llama-3.1-8b-instruct
CUSTOM_MODEL_LOCAL_LLAMA_MODEL_TYPE=local
CUSTOM_MODEL_LOCAL_LLAMA_MAX_TOKENS=2048
CUSTOM_MODEL_LOCAL_LLAMA_TEMPERATURE=0.7
CUSTOM_MODEL_LOCAL_LLAMA_TIMEOUT=30

# 示例 4：Google Gemini 模型（用于分析任务）
CUSTOM_MODEL_ANALYSIS_GEMINI_BASE_URL=https://generativelanguage.googleapis.com
CUSTOM_MODEL_ANALYSIS_GEMINI_API_KEY=your-google-api-key
CUSTOM_MODEL_ANALYSIS_GEMINI_MODEL_ID=gemini-1.5-pro
CUSTOM_MODEL_ANALYSIS_GEMINI_MODEL_TYPE=google
CUSTOM_MODEL_ANALYSIS_GEMINI_MAX_TOKENS=8192
CUSTOM_MODEL_ANALYSIS_GEMINI_TEMPERATURE=0.2

# 示例 5：HuggingFace 模型（用于特殊任务）
CUSTOM_MODEL_HF_MODEL_BASE_URL=https://api-inference.huggingface.co
CUSTOM_MODEL_HF_MODEL_API_KEY=your-hf-token
CUSTOM_MODEL_HF_MODEL_MODEL_ID=meta-llama/Llama-2-7b-chat-hf
CUSTOM_MODEL_HF_MODEL_MODEL_TYPE=huggingface
CUSTOM_MODEL_HF_MODEL_MAX_TOKENS=1024
CUSTOM_MODEL_HF_MODEL_TEMPERATURE=0.5

# === 智能体模型分配 ===

# 深度研究智能体 - 使用强大的 GPT-4 模型
AGENT_DEEP_RESEARCHER_MODEL=research_gpt

# 通用智能体 - 使用本地模型以提高效率
AGENT_GENERAL_AGENT_MODEL=local_llama

# 规划智能体 - 使用 Claude 模型以获得更好的规划能力
AGENT_PLANNING_AGENT_MODEL=planning_claude

# 浏览器使用智能体 - 使用 GPT-4 模型以获得更好的网页理解能力
AGENT_BROWSER_USE_AGENT_MODEL=research_gpt

# 深度分析智能体 - 使用 Gemini 模型以获得更好的分析能力
AGENT_DEEP_ANALYZER_MODEL=analysis_gemini

# === 其他配置 ===

# Firecrawl API Key（用于网页抓取）
FIRECRAWL_API_KEY=your-firecrawl-api-key

# Hugging Face API Key（用于 HuggingFace 模型）
HUGGINGFACE_API_KEY=your-huggingface-api-key

# 本地代理配置（可选）
# LOCAL_PROXY_BASE=http://localhost:6655
#!/usr/bin/env python3
"""
简单的Agent配置测试
测试环境变量解析功能
"""

import os

def test_environment_parsing():
    """测试环境变量解析逻辑"""
    print("🧪 测试环境变量解析逻辑")
    print("=" * 40)
    
    # 支持的Agent角色
    SUPPORTED_AGENTS = {
        "PLANNING_AGENT",
        "DEEP_RESEARCHER_AGENT", 
        "DEEP_ANALYZER_AGENT",
        "BROWSER_USE_AGENT",
    }
    
    # 支持的参数
    SUPPORTED_PARAMS = {
        "BASE_URL",
        "API_KEY", 
        "MODEL_ID",
        "MODEL_TYPE",
        "MAX_TOKENS",
        "TEMPERATURE",
        "TIMEOUT",
        "PROXY"
    }
    
    # 收集Agent配置
    agent_config_vars = {}
    
    for key, value in os.environ.items():
        # 检查是否为支持的Agent配置
        for agent_name in SUPPORTED_AGENTS:
            if key.startswith(f"{agent_name}_"):
                param_name = key[len(agent_name) + 1:]  # +1 for the underscore
                
                if param_name in SUPPORTED_PARAMS:
                    if agent_name not in agent_config_vars:
                        agent_config_vars[agent_name] = {}
                    agent_config_vars[agent_name][param_name] = value
                    break
    
    # 显示结果
    if agent_config_vars:
        print("✅ 找到以下Agent配置:")
        for agent_name, params in agent_config_vars.items():
            print(f"\n🤖 {agent_name}:")
            for param, value in params.items():
                if "API_KEY" in param and len(value) > 8:
                    # 隐藏API密钥
                    masked_value = value[:4] + "***" + value[-4:]
                    print(f"   • {param}: {masked_value}")
                else:
                    print(f"   • {param}: {value}")
            
            # 检查必需参数
            required_params = {"BASE_URL", "API_KEY", "MODEL_ID", "MODEL_TYPE"}
            missing_params = required_params - set(params.keys())
            if missing_params:
                print(f"   ⚠️  缺少必需参数: {', '.join(missing_params)}")
            else:
                print(f"   ✅ 配置完整")
    else:
        print("❌ 未找到任何Agent直接配置")
        print("\n💡 您可以设置如下环境变量:")
        print("export PLANNING_AGENT_BASE_URL='https://api.anthropic.com'")
        print("export PLANNING_AGENT_API_KEY='your-api-key'")
        print("export PLANNING_AGENT_MODEL_ID='claude-3-sonnet'")
        print("export PLANNING_AGENT_MODEL_TYPE='anthropic'")
    
    # 检查传统配置
    print(f"\n📋 检查传统Agent分配配置:")
    traditional_configs = {}
    for key, value in os.environ.items():
        if key.startswith("AGENT_") and key.endswith("_MODEL"):
            agent_name = key[6:-6]  # 去掉 "AGENT_" 和 "_MODEL"
            traditional_configs[agent_name] = value
    
    if traditional_configs:
        print("✅ 找到以下传统分配:")
        for agent, model in traditional_configs.items():
            print(f"   • {agent}: {model}")
    else:
        print("❌ 未找到传统分配配置")
    
    return len(agent_config_vars) > 0 or len(traditional_configs) > 0

def show_configuration_examples():
    """显示配置示例"""
    print(f"\n📚 配置示例:")
    print("1. Agent直接配置 (推荐) - 仅需4个必需参数:")
    print("   export PLANNING_AGENT_BASE_URL='https://api.anthropic.com'")
    print("   export PLANNING_AGENT_API_KEY='your-claude-key'")
    print("   export PLANNING_AGENT_MODEL_ID='claude-3-sonnet'")
    print("   export PLANNING_AGENT_MODEL_TYPE='anthropic'")
    print("")
    print("2. 传统分配配置:")
    print("   export CUSTOM_MODEL_GPT4_BASE_URL='https://api.openai.com/v1'")
    print("   export CUSTOM_MODEL_GPT4_API_KEY='your-openai-key'")
    print("   export CUSTOM_MODEL_GPT4_MODEL_ID='gpt-4o'")
    print("   export CUSTOM_MODEL_GPT4_MODEL_TYPE='openai'")
    print("   export AGENT_deep_researcher_agent_MODEL='GPT4'")
    print("")
    print("💡 可选参数 (MAX_TOKENS, TEMPERATURE 等) 通常不需要设置，系统会使用合理默认值")

if __name__ == "__main__":
    print("🚀 DeepAgent 简单配置测试\n")
    
    has_config = test_environment_parsing()
    
    if not has_config:
        show_configuration_examples()
    
    print(f"\n🎯 测试完成！")
    print("如需完整功能测试，请安装项目依赖后运行 test_agent_config.py")
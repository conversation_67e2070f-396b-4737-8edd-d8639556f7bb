# DeepAgent Repository Guide for Claude Code

This document provides essential information for Claude Code instances working with the DeepAgent repository.

## Project Overview

**DeepAgent** is a hierarchical multi-agent system designed for deep research tasks and general-purpose task solving. The framework uses a top-level planning agent to coordinate multiple specialized lower-level agents, enabling automated task decomposition and efficient execution across diverse domains.

### Architecture

The system adopts a two-layer structure:

1. **Top-Level Planning Agent**: Breaks down tasks into manageable sub-tasks and coordinates between agents
2. **Specialized Lower-Level Agents**:
   - **Deep Analyzer**: In-depth analysis and insight extraction
   - **Deep Researcher**: Thorough research and knowledge synthesis
   - **Browser Use**: Automated web operations and data collection
   - **General Tool Calling Agent**: Interface for various tools and APIs

## Key Directories and Files

### Core Source Structure
```
src/
├── agent/                    # Agent implementations
│   ├── planning_agent/      # Top-level coordination agent
│   ├── deep_researcher_agent/
│   ├── deep_analyzer_agent/
│   ├── browser_use_agent/
│   └── general_agent/
├── models/                   # Model management and configuration
│   ├── custom_model_parser.py  # **CRITICAL** Agent role-based config parser
│   ├── models.py              # **CRITICAL** Model manager with agent support
│   └── agent_role_config.py   # Agent configuration utilities
├── tools/                    # Tool implementations
├── config/                   # Configuration management
├── mcp/                     # MCP (Model Context Protocol) support
├── memory/                  # Memory management
└── utils/                   # Utility functions
```

### Important Configuration Files
- `main.py` - Main entry point
- `requirements.txt` - Python dependencies
- `Makefile` - Build and environment commands
- `.env.example` - **CRITICAL** Environment variable template
- `conda-setup-guide.md` - Environment setup guide
- `simple_config_test.py` - Configuration testing script

## Project Environment Notes

- **Conda Environment**: 
  - The project uses a conda environment named `deepagent`
  - Always activate the environment before running the project: `conda activate deepagent`

## Agent Role-Based Configuration System

**CRITICAL FEATURE**: The repository implements a flexible agent role-based model configuration system that allows different AI models to be assigned to different agents.

### Supported Agents
- `PLANNING_AGENT` - Task planning and coordination
- `DEEP_RESEARCHER_AGENT` - Research and information gathering
- `DEEP_ANALYZER_AGENT` - Analysis and insight extraction
- `BROWSER_USE_AGENT` - Web automation and data collection

### Configuration Methods

#### 1. Direct Agent Configuration (Recommended)
Set environment variables using the format: `{AGENT_NAME}_{PARAMETER}=value`

**Supported Parameters**:
- `BASE_URL` - API endpoint
- `API_KEY` - Authentication key
- `MODEL_ID` - Specific model identifier
- `MODEL_TYPE` - Provider type (openai, anthropic, google, azure, huggingface, local)
- `MAX_TOKENS` - Token limit
- `TEMPERATURE` - Model temperature

**Example**:
```bash
export PLANNING_AGENT_BASE_URL='https://api.anthropic.com'
export PLANNING_AGENT_API_KEY='your-claude-key'
export PLANNING_AGENT_MODEL_ID='claude-3-sonnet-20240229'
export PLANNING_AGENT_MODEL_TYPE='anthropic'
```

#### 2. Traditional Model Assignment (Legacy)
Define custom models and assign them to agents:
```bash
export CUSTOM_MODEL_GPT4_BASE_URL='https://api.openai.com/v1'
export CUSTOM_MODEL_GPT4_API_KEY='your-openai-key'
export CUSTOM_MODEL_GPT4_MODEL_ID='gpt-4o'
export CUSTOM_MODEL_GPT4_MODEL_TYPE='openai'
export AGENT_deep_researcher_agent_MODEL='GPT4'
```

### Configuration Priority
1. Direct agent configuration (highest priority)
2. Traditional agent assignment
3. Config file defaults
4. System defaults (lowest priority)

### Key Implementation Files
- `/src/models/custom_model_parser.py:_parse_agent_direct_configs()` - Parses agent environment variables
- `/src/models/models.py:get_model_for_agent()` - Retrieves model config for specific agent
- `/src/agent/agent.py:build_agent()` - Loads agent-specific models

## Environment Setup

### Using Conda (Recommended)
```bash
# Create environment
conda create -n deepagent python=3.11 -y
conda activate deepagent

# Install dependencies
pip install -r requirements.txt

# Test configuration
python simple_config_test.py
```

### Using Make Commands
```bash
make help           # Show available commands
make install        # Install all dependencies
make clean          # Remove conda environment
```

## Common Development Tasks

### Running the System
```bash
# Basic execution
python main.py

# With custom config
python main.py --config configs/your_config.py

# Run single agent example
python examples/run_general.py
```

### Testing Configuration
```bash
# Simple test (no dependencies required)
python simple_config_test.py

# Full test (requires all dependencies)
python test_agent_config.py
```

### Environment Variable Testing
```bash
# Set test variables
export PLANNING_AGENT_BASE_URL='https://api.anthropic.com'
export PLANNING_AGENT_API_KEY='test-key-12345'
export PLANNING_AGENT_MODEL_ID='claude-3-sonnet'
export PLANNING_AGENT_MODEL_TYPE='anthropic'

# Run test
python simple_config_test.py
```

## Key Features and Capabilities

- **Asynchronous Operations**: Efficient handling of multiple agents and tasks
- **Multi-Model Support**: OpenAI, Anthropic, Google, Azure, HuggingFace, local models
- **Secure Code Execution**: Sandboxed Python interpreter with configurable controls
- **Web Automation**: Browser-use agent for web scraping and interaction
- **MCP Support**: Model Context Protocol for tool integration
- **Image/Video Generation**: Imagen and Veo3 model support
- **Extensible Architecture**: Easy integration of new agents and tools

## Configuration Troubleshooting

### Common Issues
1. **Missing API Keys**: Check `.env` file or environment variables
2. **Model Not Found**: Verify model ID and provider type
3. **Configuration Priority**: Direct agent config overrides traditional assignment
4. **Import Errors**: Ensure all dependencies are installed in conda environment

### Validation Commands
```bash
# Check environment variables
env | grep -E "(PLANNING|DEEP_RESEARCHER|DEEP_ANALYZER|BROWSER_USE)_AGENT"

# Test configuration parsing
python simple_config_test.py

# Validate full system
python test_agent_config.py
```

## Development Notes

### Model Configuration System Architecture
The system implements a three-tier configuration approach:
1. **Agent Direct Config**: Environment variables with agent-specific naming
2. **Traditional Assignment**: Custom model definitions with agent mappings
3. **Config File**: Default configurations in Python config files

### Code Modification Guidelines
- Always test configuration changes with `simple_config_test.py`
- Follow existing patterns in `/src/models/` for model management
- Ensure backward compatibility with traditional configuration methods
- Use the conda environment for consistent dependency management

### Adding New Agents
1. Create agent directory in `/src/agent/`
2. Add agent name to `SUPPORTED_AGENTS` in `custom_model_parser.py`
3. Update configuration examples in `.env.example`
4. Add agent-specific model loading in `models.py`

## Quick Reference

```bash
# Setup
conda create -n deepagent python=3.11 -y && conda activate deepagent
pip install -r requirements.txt

# Configure
cp .env.example .env  # Edit with your API keys

# Test
python simple_config_test.py

# Run
python main.py
```

This system provides flexible, role-based AI model configuration while maintaining backward compatibility and supporting multiple model providers efficiently.

## Memories and Notes

### Development Workflow
- **每次修改后自动commit**: Implement automated commit after code changes
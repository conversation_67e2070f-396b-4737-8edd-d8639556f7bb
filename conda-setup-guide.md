# DeepAgent Conda环境设置指南

本指南将帮助您使用conda创建和管理DeepAgent的Python运行环境。

## 🚀 快速开始

### 1. 初始化Conda (如果conda命令无法使用)

如果您安装了conda但命令无法使用，需要先初始化：

```bash
# 对于通过Homebrew安装的miniconda
source /opt/homebrew/Caskroom/miniconda/base/etc/profile.d/conda.sh

# 对于标准安装的miniconda/anaconda
source ~/miniconda3/etc/profile.d/conda.sh
# 或
source ~/anaconda3/etc/profile.d/conda.sh
```

### 2. 创建DeepAgent环境

```bash
# 创建名为deepagent的Python 3.11环境
conda create -n deepagent python=3.11 -y
```

### 3. 激活环境

```bash
conda activate deepagent
```

### 4. 验证环境

```bash
python --version  # 应该显示 Python 3.11.x
pip --version     # 确认pip可用
```

## 📦 安装依赖

### 方法1: 安装所有依赖 (完整功能)

```bash
# 激活环境
conda activate deepagent

# 安装所有依赖 (需要一些时间)
pip install -r requirements.txt
```

### 方法2: 最小化安装 (仅配置功能)

如果您只需要测试配置功能，可以安装基础依赖：

```bash
# 激活环境
conda activate deepagent

# 安装基础依赖
pip install python-dotenv rich numpy pandas
```

## 🧪 测试安装

### 基本配置测试

```bash
# 激活环境
conda activate deepagent

# 运行基本配置测试
python simple_config_test.py
```

### 完整功能测试 (需要完整依赖)

```bash
# 激活环境
conda activate deepagent

# 运行完整测试 (需要先安装所有依赖)
python test_agent_config.py
```

## ⚙️ 配置DeepAgent

### 1. 复制配置模板

```bash
cp .env.example .env
```

### 2. 编辑配置文件

编辑 `.env` 文件，配置您的Agent模型：

```bash
# 示例配置
PLANNING_AGENT_BASE_URL=https://api.anthropic.com
PLANNING_AGENT_API_KEY=your-claude-api-key-here
PLANNING_AGENT_MODEL_ID=claude-3-sonnet-20240229
PLANNING_AGENT_MODEL_TYPE=anthropic

DEEP_RESEARCHER_AGENT_BASE_URL=https://api.openai.com/v1
DEEP_RESEARCHER_AGENT_API_KEY=your-openai-api-key-here
DEEP_RESEARCHER_AGENT_MODEL_ID=gpt-4o
DEEP_RESEARCHER_AGENT_MODEL_TYPE=openai
```

### 3. 测试配置

```bash
# 设置测试环境变量
export PLANNING_AGENT_BASE_URL='https://api.anthropic.com'
export PLANNING_AGENT_API_KEY='test-key-12345'
export PLANNING_AGENT_MODEL_ID='claude-3-sonnet'
export PLANNING_AGENT_MODEL_TYPE='anthropic'

# 运行测试
python simple_config_test.py
```

## 🏃‍♂️ 运行DeepAgent

### 基本运行

```bash
# 激活环境
conda activate deepagent

# 运行DeepAgent
python main.py
```

### 带配置运行

```bash
# 激活环境
conda activate deepagent

# 加载.env文件并运行
python main.py
```

## 🛠️ 环境管理

### 查看所有conda环境

```bash
conda env list
```

### 删除环境 (如果需要重新创建)

```bash
conda env remove -n deepagent
```

### 更新环境

```bash
# 激活环境
conda activate deepagent

# 更新pip包
pip install --upgrade -r requirements.txt
```

### 导出环境配置

```bash
# 激活环境
conda activate deepagent

# 导出环境配置
conda env export > deepagent-environment.yml
```

### 从配置文件创建环境

```bash
# 从导出的配置创建环境
conda env create -f deepagent-environment.yml
```

## 🔧 常见问题解决

### 问题1: conda命令不存在

**解决方案**:
```bash
# 找到conda安装位置
which conda

# 如果显示函数定义，需要初始化
source /opt/homebrew/Caskroom/miniconda/base/etc/profile.d/conda.sh

# 或者添加到shell配置文件
echo 'source /opt/homebrew/Caskroom/miniconda/base/etc/profile.d/conda.sh' >> ~/.zshrc
```

### 问题2: 依赖安装失败

**解决方案**:
```bash
# 更新pip
pip install --upgrade pip

# 分批安装依赖
pip install python-dotenv rich numpy pandas
pip install openai anthropic
pip install transformers datasets
# 继续安装其他包...
```

### 问题3: 权限问题

**解决方案**:
```bash
# 确保在conda环境中
conda activate deepagent

# 检查环境位置
which python
which pip

# 如果仍有问题，尝试用户安装
pip install --user package-name
```

### 问题4: 环境冲突

**解决方案**:
```bash
# 删除环境重新创建
conda env remove -n deepagent
conda create -n deepagent python=3.11 -y
conda activate deepagent
```

## 📋 常用命令总结

```bash
# 环境管理
conda create -n deepagent python=3.11 -y    # 创建环境
conda activate deepagent                     # 激活环境
conda deactivate                            # 退出环境
conda env list                              # 列出所有环境
conda env remove -n deepagent               # 删除环境

# 包管理
pip install -r requirements.txt             # 安装所有依赖
pip list                                    # 列出已安装包
pip freeze > requirements-freeze.txt        # 导出依赖列表

# 测试和运行
python simple_config_test.py               # 基本配置测试
python test_agent_config.py                # 完整功能测试
python main.py                             # 运行DeepAgent
```

## 🎯 下一步

1. **配置API密钥**: 编辑 `.env` 文件添加您的API密钥
2. **运行测试**: 确保配置正确工作
3. **开始使用**: 运行 `python main.py` 开始使用DeepAgent

## 📚 更多资源

- [Agent模型配置指南](docs/agent-model-configuration-guide.md)
- [项目README](README.md)
- [配置示例文件](.env.example)

---

如有问题，请检查上述故障排除部分或提交Issue。